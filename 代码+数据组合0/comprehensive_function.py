#贝克21项抑郁量表（BDI）
import pandas as pd


def classify_depression(df):

    # 定义一个函数，根据score列的值返回抑郁程度分类
    def classify_score(row):
        score = row['score']
        if 0 <= score <= 9:
            return '最小程度的抑郁'
        elif 10 <= score <= 18:
            return '轻度抑郁'
        elif 19 <= score <= 29:
            return '中度抑郁'
        elif 30 <= score <= 63:
            return '重度抑郁'
        else:
            return '未知分类'  # 如果score值不在这个范围内，可以归为未知分类

    # 应用分类函数到每一行，并创建新的classification列
    df['classification'] = df.apply(classify_score, axis=1)

    return df
def calculate_score(excel_file_path):

    def convert_to_score(value):
        # 定义一部字典来映射特定的值到分数
        score_map = {
            '0': 0,
            '1': 1,
            '1a': 1,
            '1b': 1,
            '2': 2,
            '2a': 2,
            '2b': 2,
            '3': 3,
            '3a': 3,
            '3b': 3
        }

        # 将输入值转换为字符串，然后查找对应的分数
        return score_map.get(str(value).strip(), 0)
    df = pd.read_excel(excel_file_path)

    # 定义列的范围，例如Q1到Q21
    columns_to_apply = [f"Q{i}" for i in range(1, 22)]

    # 确保这些列只包含字符串类型的数据，然后应用convert_to_score函数
    for column in columns_to_apply:
        df[column] = df[column].astype(str)  # 确保列中的所有数据都是字符串类型
        df[column] = df[column].apply(convert_to_score)

    # 计算指定列的分数之和作为新的'score'列
    df['score'] = df[columns_to_apply].sum(axis=1)
    df['score'] = df['score'].astype(int)  # 确保'score'列是整数类型

    return df

def real_score():
    def calculate_total_score(input_items):
        def sum_score(value):
            # 定义计分映射
            score_map = {
                '0': 0,
                '1': 1,
                '1a': 1,
                '1b': 1,
                '2': 2,
                '2a': 2,
                '2b': 2,
                '3': 3,
                '3a': 3,
                '3b': 3
            }
            # 返回对应的分数，如果项不在映射中，则默认为0
            return score_map.get(str(value).strip(), 0)
        # 将输入字符串按空格分割成列表
        items = input_items.split()
        # 计算总分
        total_score = sum(sum_score(item) for item in items)
        return total_score

    while 1:

        # 从用户那里获取输入
        input_items = input("请输入一系列以空格分隔的项：")
        # 计算总分
        total_score = calculate_total_score(input_items)
        # 输出总分
        print(f"这些项的总分为：{total_score}")
        # 出口：
        if input_items == '0':
            break

def relative_difference(df):
    # 使用 abs 函数确保分子是绝对值
    df['relative_difference'] = abs(df['score'] - df['real_score']) / ((df['score'] + df['real_score']) / 2)

    return df


excel_file_path = '2020T2.xlsx'  # 替换为Excel文件的路径
df = pd.read_excel(excel_file_path)

# 调用计分函数
# df = calculate_score(excel_file_path)

# 调用分类函数
# df = classify_depression(df)

# 调用真值函数
real_score()

# 调用相对差值函数
# df = relative_difference(df)

# 将最终的DataFrame写回到Excel文件
# df.to_excel(excel_file_path, index=False, engine='openpyxl')