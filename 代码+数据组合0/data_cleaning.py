# eRisk数据集数据清洗和格式化
def data_writing():
    """程序要求：
    有三个文件夹a, b, c, 希望将清洗好的数据分别放在三个.xlsx文件d, e, f中。对于这三个excel，顺序创建以下列：individual, text。我会from
    Big_Five import *, 读取这个脚本的Big_Five列表，每读一个元素就在这三个excel创建一个列，列的名称是Qi，i是该元素在Big_Five列表的下标
    对于文件夹中，每个xml文件代表一个用户，每个xml中有一个Individual和很多text标签，我希望上述程序能实现以下功能：
    对于创建的三个excel d, e, f，分别写入文件夹a, b, c中的文件内容。对于每个excel的一行，individual列对应一个xml中individual标签的内容，
    text列对应该xml中所有text标签中的内容，也就是要把一个xml中所有text标签中内容提取出来连在一起填入excel中该行的text列"""

    import os
    import pandas as pd
    import xml.etree.ElementTree as ET
    import CLEF_eRisk

    # 定义文件夹和对应的Excel文件名
    folder_to_excel = {
        # 'D:\YLD\Scientific_Research_L\文本分析预测\数据集\eRisk2019_78MB\eRisk2019\eRisk2019_T3\T3': '2019T3.xlsx',
        # 'D:\YLD\Scientific_Research_L\文本分析预测\数据集\eRisk2020_27MB\eRisk2020\eRisk2020_T2\T2\DATA': '2020T2.xlsx',
        # 'D:\YLD\Scientific_Research_L\文本分析预测\数据集\eRisk2020_27MB\eRisk2020\eRisk2020_T2_TRAINING_DATA': '2020T2_training_data.xlsx',
        # 'D:\YLD\Scientific_Research_L\文本分析预测\数据集\eRisk2021_T3_12MB\T3\eRisk2021_T3_Collection':'2021T3.xlsx'
    }

    # 初始化一个空的DataFrame，用于存储数据
    data_frames = {excel_file: pd.DataFrame(columns=['individual', 'text']) for excel_file in folder_to_excel.values()}

    # 遍历每个文件夹，读取XML文件，并填充DataFrame
    for folder, excel_file in folder_to_excel.items():
        new_rows = []  # 用于存储新行的列表
        for root, dirs, files in os.walk(folder):
            for file in files:
                if file.endswith('.xml'):
                    file_path = os.path.join(root, file)
                    tree = ET.parse(file_path)
                    root_element = tree.getroot()

                    # 提取<INDIVIDUAL>标签的内容
                    individual_id = root_element.find('ID').text

                    # 提取所有<TEXT>标签的内容并合并为一个字符串
                    texts = [text_element.text.strip() for text_element in root_element.findall('.//TEXT') if
                             text_element.text]
                    text_content = ' '.join(texts)

                    # 创建新行的字典
                    new_rows.append({'individual': individual_id, 'text': text_content})

        # 使用pd.DataFrame一次性创建新的DataFrame
        new_df = pd.DataFrame(new_rows)

        # 将新DataFrame与旧DataFrame合并，并替换原来的DataFrame
        if new_df.shape[0] > 0:  # 如果new_df不为空
            data_frames[excel_file] = pd.concat([data_frames[excel_file], new_df], ignore_index=True)

        # 为eRisk列表中的每个元素添加列，初始值为None
        for i in range(len(CLEF_eRisk.eRisk)):
            data_frames[excel_file][f'Q{i + 3}'] = None

        # 将DataFrame写入Excel文件
        data_frames[excel_file].to_excel(excel_file, index=False, engine='openpyxl')

    print("数据筛选和写入Excel完成。")

def data_cleaning():
    """1.文本规范化：将所有文本转换为统一的大小写（通常是小写），以减少变体带来的差异。
    2.分句：将长文本分割成句子。这可以通过句号、问号、感叹号等句子终止符来实现。
    3.去除多余的空格和特殊字符：清理文本中的多余空格、制表符和特殊字符。
    4.去除停用词：停用词是指在文本中频繁出现但对分析没有太大意义的词，如“the”，“ is ”，“ in ”等。
    5.词干提取或词形还原：这有助于将词汇统一到基本形式，减少词形变化的影响。
    6.拼写检查：纠正文本中的拼写错误。
    7.去除无意义的填充词：例如，“um”，“uh”，“like”等在口语中常用但在书面语中无意义的词汇。"""

    import pandas as pd
    import re
    from nltk.tokenize import sent_tokenize
    import nltk

    # # 下载punkt资源包
    # nltk.download('punkt')

    # 定义清洗和格式化文本的函数
    def formatted_text(input_text):
        # 将换行符替换为一个空格
        cleaned_text = re.sub(r'\s+', ' ', input_text.strip())

        # 分句
        sentences = sent_tokenize(cleaned_text)

        # 将每个句子用双引号括起来，并用~分隔
        formatted_text = '~'.join('"{}"'.format(sentence) for sentence in sentences)

        # 去除所有双引号
        # formatted_text = input_text.replace('~~', '~')
        return formatted_text

    # 定义要处理的Excel文件和输出文件的映射
    excel_files = [
        # '2019T3.xlsx',
        # '2020T2.xlsx',
        # '2020T2_training_data.xlsx',
        # '2021T3.xlsx'
    ]

    # 遍历每个Excel文件，处理text列，并重命名列标题
    for file in excel_files:
        df = pd.read_excel(file)  # 读取Excel文件
        df['formatted_text'] = df['formatted_text'].apply(formatted_text)  # 应用format_text函数并创建新列
        df.rename(columns={'text': 'formatted_text'}, inplace=True)  # 重命名列标题
        df.to_excel(file, index=False, engine='openpyxl')  # 保存并覆盖原文件

    print("数据清洗和格式化完成，并已更新原文件。")


# 执行区
# 函数1：数据筛选和写入excel
data_writing()

# 函数2：数据清洗
data_cleaning()
