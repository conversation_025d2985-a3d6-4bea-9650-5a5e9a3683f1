from env_preparation import *
from CLEF_eRisk import *

tokenizer_sentence_transformer = AutoTokenizer.from_pretrained('D:\YLD\Scientific_Research_L\文本分析预测\模型')
model_sentence_transformer = AutoModel.from_pretrained('D:\YLD\Scientific_Research_L\文本分析预测\模型')


def similarity_calculation(i, list1, list2):
    # 提示当前循环的问题编号
    print(f"Current question is number {i + 1}.")
    # 初始化字典，用于存储分词后的句子
    questionare_tokenized = {'input_ids': [], 'attention_mask': []}

    # 遍历eRisk列表中字典的键（即句子）
    for sentence in list(list1[i].keys()):
        # 使用tokenizer对句子进行编码，并添加到字典列表中
        # encode_plus方法返回的是一个字典，包含input_ids和attention_mask等字段
        new_tokens = tokenizer_sentence_transformer.encode_plus(sentence, max_length=512, truncation=True,
                                                                padding='max_length', return_tensors='pt')
        questionare_tokenized['input_ids'].append(new_tokens['input_ids'][0])
        questionare_tokenized['attention_mask'].append(new_tokens['attention_mask'][0])

    # 将存储input_ids和attention_mask的列表转换为单一的tensor
    questionare_tokenized['input_ids'] = torch.stack(questionare_tokenized['input_ids'])
    questionare_tokenized['attention_mask'] = torch.stack(questionare_tokenized['attention_mask'])

    # 使用模型对编码后的句子进行嵌入，提取last_hidden_state作为嵌入向量
    embedding_questionare = model_sentence_transformer(**questionare_tokenized).last_hidden_state

    # 对list1[i]的嵌入向量应用掩码，然后计算掩码后向量的和
    # 首先，将attention_mask扩展至嵌入向量的大小，以便于元素级乘法操作
    mask = questionare_tokenized['attention_mask'].unsqueeze(-1).expand(embedding_questionare.size()).float()
    # 应用掩码：只保留有效的词嵌入，忽略因填充(padding)而添加的部分
    masked_embeddings = embedding_questionare * mask
    # 对每个文档的掩码后嵌入向量进行求和，得到一个代表整个文档的向量
    summed_questionare = torch.sum(masked_embeddings, 1).detach().numpy()

    # 对用户查询进行相同的处理流程
    result = []
    list_tem = []
    j = 0
    for ele in list2:

        # 重置user_query_tokenized字典，以便处理新的查询
        user_query_tokenized = {'input_ids': [], 'attention_mask': []}

        # 一个用户文本终止结算：
        if ele == 'E-O-P':

            # 根据平均余弦相似度找出最相似句子的索引
            # 计算平均值
            sum_most_similar_sentence_index = 0
            for item in list_tem:
                sum_most_similar_sentence_index += item
            avg_most_similar_sentence_index = sum_most_similar_sentence_index / len(list_tem)

            # 根据索引找出最相似的选项
            avg_most_similar_sentence_result = list(eRisk[i].values())[round(avg_most_similar_sentence_index)]
            print(f"result {j + 1} = {avg_most_similar_sentence_result}")
            result.append(avg_most_similar_sentence_result)

            # 更新过程型变量
            list_tem.clear()
            j += 1
            continue

        # 使用tokenizer将用户查询编码，并添加到相应的字典列表中
        new_tokens = tokenizer_sentence_transformer.encode_plus(ele, max_length=512, truncation=True,
                                                                padding='max_length', return_tensors='pt')
        user_query_tokenized['input_ids'].append(new_tokens['input_ids'][0])
        user_query_tokenized['attention_mask'].append(new_tokens['attention_mask'][0])

        # 将存储input_ids和attention_mask的列表转换为单一的tensor
        user_query_tokenized['input_ids'] = torch.stack(user_query_tokenized['input_ids'])
        user_query_tokenized['attention_mask'] = torch.stack(user_query_tokenized['attention_mask'])

        # 使用模型获取用户查询的嵌入向量
        embedding_user_query = model_sentence_transformer(**user_query_tokenized).last_hidden_state

        # 对用户查询的嵌入向量应用掩码
        mask_user_query = user_query_tokenized['attention_mask'].unsqueeze(-1).expand(
            embedding_user_query.size()).float()
        masked_embeddings_user_query = embedding_user_query * mask_user_query

        # 计算掩码后的嵌入向量的和
        summed_user_query = torch.sum(masked_embeddings_user_query, 1).detach().numpy()

        # 计算用户查询向量与所有文档向量之间的余弦相似度
        cosine_similarities = cosine_similarity([summed_user_query[0]], summed_questionare[:])

        # 存储切片的相似度
        most_similar_sentence_index = np.argmax(cosine_similarities)
        list_tem.append(most_similar_sentence_index)

        # 使用找到的索引从原始数据中检索最相似的句子
        # 假设list1[i]是一个字典，其键为句子，值为选项号
        # most_similar_sentence_key = list(list1[i].keys())[most_similar_sentence_index]
        # most_similar_sentence_value = list(list1[i].values())[most_similar_sentence_index]

    return result

    # # 打印出与用户查询最相似的句子及其相似度得分
    # print(f"与user_text最相似的句子是“{most_similar_sentence_key}”")
    # print(f"值：{most_similar_sentence_value}，相似度得分为{cosine_similarities[0][most_similar_sentence_index].item()}")


def execution():
    # 读取Excel文件:
    excel_file = "人造数据集测试.xlsx"
    df = pd.read_excel(excel_file)

    # 指定所需列
    start_column = 'Q1'
    end_column = 'Q21'
    user_column = 'individual'

    # 确定要遍历的列的索引范围
    start_idx = df.columns.get_loc(start_column)
    end_idx = df.columns.get_loc(end_column)

    # 使用 ExcelWriter 以追加模式打开文件
    i = 0
    # 遍历指定范围内的列
    for column_name in df.columns[start_idx:end_idx + 1]:
        result = similarity_calculation(i, eRisk, test)
        for index, row in df.iterrows():  # 遍历行
            # 将计算得到的 value 写入到 DataFrame 的相应位置
            df.at[index, column_name] = result[index]
            # 访问同一行的 'individual' 列的值
            user_name = df.at[index, user_column]
            # 打印当前列名、用户名和值并将相似度得分写入excel
            print(f'列: {column_name}, 用户名: {user_name}, 答案: {result[index]}')
        i += 1
    # 将修改后的 DataFrame 写回到原始 Excel 文件的同一工作表中
    df.to_excel(excel_file, index=False, engine='openpyxl')


# 调用 execution 函数
execution()


