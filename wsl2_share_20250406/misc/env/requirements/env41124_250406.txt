absl-py==2.1.0
accelerate==1.1.1
ace_tools==0.0
aiohappyeyeballs==2.4.3
aiohttp==3.11.7
aiosignal==1.3.1
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.6.2.post1
asttokens @ file:///home/<USER>/feedstock_root/build_artifacts/asttokens_1698341106958/work
astunparse==1.6.3
attrs==24.2.0
audeer==2.2.0
audformat==1.3.1
audinterface==1.2.2
audiofile==1.5.0
audioread==3.0.1
audmath==1.4.1
audobject==0.7.11
audresample==1.3.3
av==13.1.0
be_great==0.0.8
beautifulsoup4==4.13.3
bitsandbytes==0.44.1
bleach==6.2.0
blinker==1.9.0
blobfile==3.0.0
boto3==1.35.68
botocore==1.35.68
Brotli @ file:///croot/brotli-split_1714483155106/work
cachetools==5.5.2
category-encoders==2.6.4
certifi @ file:///croot/certifi_1725551672989/work/certifi
cffi==1.17.1
charset-normalizer @ file:///croot/charset-normalizer_1721748349566/work
click==8.1.7
cloudpickle==3.1.0
cmake==3.31.6
comm @ file:///home/<USER>/feedstock_root/build_artifacts/comm_1710320294760/work
contourpy==1.3.1
copulas==0.12.0
ctgan==0.10.2
cut-cross-entropy==24.11.4
cycler==0.12.1
dataclasses-json==0.6.7
datasets==3.1.0
debugpy @ file:///work/perseverance-python-buildout/croot/debugpy_1698884710808/work
decorator @ file:///home/<USER>/feedstock_root/build_artifacts/decorator_1641555617451/work
deepecho==0.6.1
deepface==0.0.93
defusedxml==0.7.1
dill==0.3.8
diskcache==5.6.3
distlib==0.3.9
distro==1.9.0
docker-pycreds==0.4.0
docstring_parser==0.16
einops==0.8.1
evaluate==0.4.3
exceptiongroup @ file:///home/<USER>/feedstock_root/build_artifacts/exceptiongroup_1720869315914/work
executing @ file:///home/<USER>/feedstock_root/build_artifacts/executing_1725214404607/work
faiss-cpu==1.10.0
Faker==33.0.0
fastjsonschema==2.21.1
filelock @ file:///work/perseverance-python-buildout/croot/filelock_1701733993137/work
fire==0.7.0
Flask==3.1.0
flask-cors==5.0.1
flatbuffers==25.2.10
fonttools==4.55.0
frozenlist==1.5.0
fsspec==2024.9.0
gast==0.6.0
gdown==5.2.0
gensim==4.3.3
gitdb==4.0.11
GitPython==3.1.43
google-pasta==0.2.0
graphviz==0.20.3
greenlet==3.1.1
grpcio==1.68.0
gunicorn==23.0.0
h11==0.14.0
h5py==3.13.0
hf_transfer==0.1.8
httpcore==0.16.3
httpx==0.23.3
httpx-sse==0.4.0
huggingface-hub==0.26.2
idna @ file:///croot/idna_1714398848350/work
imageio==2.37.0
imbalanced-learn==0.12.4
importlib_metadata @ file:///home/<USER>/feedstock_root/build_artifacts/importlib-metadata_1726082825846/work
IProgress==0.4
ipykernel @ file:///home/<USER>/feedstock_root/build_artifacts/ipykernel_1719845459717/work
ipython @ file:///home/<USER>/feedstock_root/build_artifacts/ipython_1729866374957/work
ipywidgets==8.1.5
iso3166==2.1.1
iso639-lang==2.5.1
itsdangerous==2.2.0
jax==0.5.3
jaxlib==0.5.3
jedi @ file:///home/<USER>/feedstock_root/build_artifacts/jedi_1731317204262/work
jieba==0.42.1
Jinja2 @ file:///croot/jinja2_1730902924303/work
jiter==0.8.2
jmespath==1.0.1
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter_client @ file:///home/<USER>/feedstock_root/build_artifacts/jupyter_client_1726610684920/work
jupyter_core @ file:///home/<USER>/feedstock_root/build_artifacts/jupyter_core_1727163409502/work
jupyterlab_pygments==0.3.0
jupyterlab_widgets==3.0.13
kagglehub==0.3.6
keras==3.9.2
kiwisolver==1.4.7
langchain==0.3.19
langchain-community==0.3.18
langchain-core==0.3.40
langchain-experimental==0.3.4
langchain-openai==0.2.12
langchain-text-splitters==0.3.6
langsmith==0.2.3
lazy_loader==0.4
libclang==18.1.1
librosa==0.10.2.post1
lightgbm==4.5.0
llama_cpp_python @ https://github.com/abetlen/llama-cpp-python/releases/download/v0.3.4-cu124/llama_cpp_python-0.3.4-cp312-cp312-linux_x86_64.whl#sha256=c3e1cab50aca2194d55c4164c2d01304919f0533e9a9aedb1390c2bfd95dd6d0
llvmlite==0.43.0
lxml==5.3.0
lz4==4.4.4
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe @ file:///work/perseverance-python-buildout/croot/markupsafe_1707342994941/work
marshmallow==3.26.1
matplotlib==3.9.2
matplotlib-inline @ file:///home/<USER>/feedstock_root/build_artifacts/matplotlib-inline_1713250518406/work
mdurl==0.1.2
mediapipe==0.10.21
mistune==3.1.3
mkl-service==2.4.0
mkl_fft @ file:///io/mkl313/mkl_fft_1730824109137/work
mkl_random @ file:///io/mkl313/mkl_random_1730823916628/work
ml_dtypes==0.5.1
mpmath @ file:///work/perseverance-python-buildout/croot/mpmath_1698864994882/work
msgpack==1.1.0
mtcnn==1.0.0
mtkresearch==0.3.1
multidict==6.1.0
multiprocess==0.70.16
mypy-extensions==1.0.0
namex==0.0.8
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
nest_asyncio @ file:///home/<USER>/feedstock_root/build_artifacts/nest-asyncio_1705850609492/work
networkx @ file:///croot/networkx_1717597493534/work
ninja==********
numba==0.60.0
numpy==1.26.4
nvidia-nccl-cu12==2.23.4
omegaconf==2.3.0
openai==1.57.4
opencv-contrib-python==*********
opencv-python==*********
opensmile==2.5.0
opt_einsum==3.4.0
optree==0.14.1
orjson==3.10.12
oyaml==1.0
packaging @ file:///home/<USER>/feedstock_root/build_artifacts/packaging_1731802491770/work
pandas==2.2.3
pandocfilters==1.5.1
parso @ file:///home/<USER>/feedstock_root/build_artifacts/parso_1712320355065/work
patsy==1.0.1
peft==0.13.2
pexpect @ file:///home/<USER>/feedstock_root/build_artifacts/pexpect_1706113125309/work
pickleshare @ file:///home/<USER>/feedstock_root/build_artifacts/pickleshare_1602536217715/work
pillow @ file:///croot/pillow_1731594689713/work
platformdirs @ file:///home/<USER>/feedstock_root/build_artifacts/platformdirs_1726613481435/work
plotly==5.24.1
pooch==1.8.2
prettytable==3.6.0
prompt_toolkit @ file:///home/<USER>/feedstock_root/build_artifacts/prompt-toolkit_1727341649933/work
propcache==0.2.0
protobuf==4.25.6
psutil==6.1.0
ptyprocess @ file:///home/<USER>/feedstock_root/build_artifacts/ptyprocess_1609419310487/work/dist/ptyprocess-0.7.0-py2.py3-none-any.whl
pure_eval @ file:///home/<USER>/feedstock_root/build_artifacts/pure_eval_1721585709575/work
pyarrow==18.0.0
pycparser==2.22
pycryptodomex==3.21.0
pydantic==2.10.3
pydantic-settings==2.8.1
pydantic_core==2.27.1
pydub==0.25.1
Pygments @ file:///home/<USER>/feedstock_root/build_artifacts/pygments_1714846767233/work
PyJWT==2.8.0
pyparsing==3.2.0
PySocks @ file:///work/perseverance-python-buildout/croot/pysocks_1698845478203/work
python-dateutil @ file:///home/<USER>/feedstock_root/build_artifacts/python-dateutil_1731919281354/work
python-dotenv==1.0.1
pytz==2024.2
PyYAML @ file:///croot/pyyaml_1728657952215/work
pyzmq @ file:///croot/pyzmq_1705605076900/work
qwen-vl-utils==0.0.8
rdt==1.13.1
referencing==0.36.2
regex==2024.11.6
requests @ file:///croot/requests_1730999120400/work
requests-toolbelt==1.0.0
retina-face==0.0.17
rfc3986==1.5.0
rich==13.9.4
rpds-py==0.23.1
s3transfer==0.10.4
safetensors==0.4.5
scikit-image==0.25.2
scikit-learn==1.5.2
scipy==1.13.1
sdmetrics==0.17.0
sdv==1.17.2
seaborn==0.13.2
sentence-transformers==3.4.1
sentencepiece==0.2.0
sentry-sdk==2.19.0
setproctitle==1.3.4
setuptools==75.8.2
shap==0.46.0
shtab==1.7.1
six @ file:///home/<USER>/feedstock_root/build_artifacts/six_1620240208055/work
slicer==0.0.8
smart-open==7.1.0
smmap==5.0.1
sniffio==1.3.1
sounddevice==0.5.1
soundfile==0.12.1
soupsieve==2.6
soxr==0.5.0.post1
SQLAlchemy==2.0.36
stack-data @ file:///home/<USER>/feedstock_root/build_artifacts/stack_data_1669632077133/work
statsmodels==0.14.4
sympy==1.13.1
tabgan==2.2.3
tenacity==9.0.0
tensorboard==2.19.0
tensorboard-data-server==0.7.2
tensorflow==2.19.0
termcolor==3.0.1
threadpoolctl==3.5.0
tifffile==2025.3.30
tiktoken==0.8.0
timm==1.0.15
tinycss2==1.4.0
tokenizers==0.20.3
torch==2.5.1
torchao==0.8.0
torchaudio==2.5.1
torchtune==0.5.0
torchvision==0.20.1
tornado @ file:///croot/tornado_1718740109488/work
tqdm==4.67.0
traitlets @ file:///home/<USER>/feedstock_root/build_artifacts/traitlets_1713535121073/work
transformers==4.46.3
triton==3.1.0
trl @ git+https://github.com/huggingface/trl.git@672c96546d9cae7a6d0afba381b189bb3cb2e8b5
typeguard==2.13.3
typing-inspect==0.9.0
typing_extensions==4.12.2
tyro==0.9.1
tzdata==2024.2
unsloth @ file:///mnt/c/working/programming/python/resources/github_resources/unsloth
unsloth_zoo==2024.11.7
urllib3 @ file:///croot/urllib3_1727769808118/work
virtualenv==20.28.0
volcengine-python-sdk==1.0.126
wandb==0.18.7
wcwidth @ file:///home/<USER>/feedstock_root/build_artifacts/wcwidth_1704731205417/work
webencodings==0.5.1
Werkzeug==3.1.3
wheel==0.45.1
widgetsnbextension==4.0.13
wordcloud==1.9.4
wrapt==1.17.2
xformers==0.0.28.post3
xgboost==2.1.2
xxhash==3.5.0
yarl==1.18.0
ydata-core==0.7.0
ydata-datascience==0.7.0
ydata-sdk==1.0.1
ydata-synthetic==2.0.0
zhipuai==2.1.5.20250106
zipp @ file:///home/<USER>/feedstock_root/build_artifacts/zipp_1731262100163/work
