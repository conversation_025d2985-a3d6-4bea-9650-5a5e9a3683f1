name: env41124
channels:
  - pytorch
  - nvidia
  - defaults
  - conda-forge
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_gnu
  - asttokens=2.4.1=pyhd8ed1ab_0
  - blas=1.0=mkl
  - brotli-python=1.0.9=py312h6a678d5_8
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2024.9.24=h06a4308_0
  - certifi=2024.8.30=py312h06a4308_0
  - charset-normalizer=3.3.2=pyhd3eb1b0_0
  - comm=0.2.2=pyhd8ed1ab_0
  - cuda-cudart=12.4.127=0
  - cuda-cupti=12.4.127=0
  - cuda-libraries=12.4.1=0
  - cuda-nvrtc=12.4.127=0
  - cuda-nvtx=12.4.127=0
  - cuda-opencl=12.6.77=0
  - cuda-runtime=12.4.1=0
  - cuda-version=12.6=3
  - debugpy=1.6.7=py312h6a678d5_0
  - decorator=5.1.1=pyhd8ed1ab_0
  - exceptiongroup=1.2.2=pyhd8ed1ab_0
  - executing=2.1.0=pyhd8ed1ab_0
  - expat=2.6.3=h6a678d5_0
  - ffmpeg=4.3=hf484d3e_0
  - filelock=3.13.1=py312h06a4308_0
  - freetype=2.12.1=h4a9f257_0
  - giflib=5.2.2=h5eee18b_0
  - gmp=6.2.1=h295c915_3
  - gnutls=3.6.15=he1e5248_0
  - idna=3.7=py312h06a4308_0
  - importlib-metadata=8.5.0=pyha770c72_0
  - intel-openmp=2023.1.0=hdb19cb5_46306
  - ipykernel=6.29.5=pyh3099207_0
  - ipython=8.29.0=pyh707e725_0
  - jedi=0.19.2=pyhff2d567_0
  - jinja2=3.1.4=py312h06a4308_1
  - jpeg=9e=h5eee18b_3
  - jupyter_client=8.6.3=pyhd8ed1ab_0
  - jupyter_core=5.7.2=pyh31011fe_1
  - krb5=1.21.3=h143b758_0
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - lerc=3.0=h295c915_0
  - libcublas=********=0
  - libcufft=********=0
  - libcufile=********=0
  - libcurand=*********=0
  - libcusolver=********=0
  - libcusparse=**********=0
  - libdeflate=1.17=h5eee18b_1
  - libedit=3.1.20230828=h5eee18b_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc=14.2.0=h77fa898_1
  - libgcc-ng=14.2.0=h69a702a_1
  - libgomp=14.2.0=h77fa898_1
  - libiconv=1.16=h5eee18b_3
  - libidn2=2.3.4=h5eee18b_0
  - libjpeg-turbo=2.0.0=h9bf148f_0
  - libnpp=*********=0
  - libnvfatbin=12.6.77=0
  - libnvjitlink=12.4.127=0
  - libnvjpeg=**********=0
  - libpng=1.6.39=h5eee18b_0
  - libsodium=1.0.20=h4ab18f5_0
  - libstdcxx=14.2.0=hc0a3c3a_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.19.0=h5eee18b_0
  - libtiff=4.5.1=h6a678d5_0
  - libunistring=0.9.10=h27cfd23_0
  - libuuid=1.41.5=h5eee18b_0
  - libwebp=1.3.2=h11a3e52_0
  - libwebp-base=1.3.2=h5eee18b_1
  - llvm-openmp=14.0.6=h9e868ea_0
  - lz4-c=1.9.4=h6a678d5_1
  - markupsafe=2.1.3=py312h5eee18b_0
  - matplotlib-inline=0.1.7=pyhd8ed1ab_0
  - mkl=2023.1.0=h213fc3f_46344
  - mkl-service=2.4.0=py312h5eee18b_1
  - mkl_fft=1.3.11=py312h5eee18b_0
  - mkl_random=1.2.8=py312h526ad5a_0
  - mpmath=1.3.0=py312h06a4308_0
  - ncurses=6.4=h6a678d5_0
  - nest-asyncio=1.6.0=pyhd8ed1ab_0
  - nettle=3.7.3=hbbd107a_1
  - networkx=3.2.1=py312h06a4308_0
  - openh264=2.1.1=h4ff587b_0
  - openjpeg=2.5.2=he7f1fd0_0
  - openssl=3.4.0=hb9d3cd8_0
  - packaging=24.2=pyhff2d567_1
  - parso=0.8.4=pyhd8ed1ab_0
  - pexpect=4.9.0=pyhd8ed1ab_0
  - pickleshare=0.7.5=py_1003
  - pillow=11.0.0=py312hfdbf927_0
  - platformdirs=4.3.6=pyhd8ed1ab_0
  - prompt-toolkit=3.0.48=pyha770c72_0
  - ptyprocess=0.7.0=pyhd3deb0d_0
  - pure_eval=0.2.3=pyhd8ed1ab_0
  - pygments=2.18.0=pyhd8ed1ab_0
  - pysocks=1.7.1=py312h06a4308_0
  - python=3.12.7=h5148396_0
  - python-dateutil=2.9.0.post0=pyhff2d567_0
  - pytorch=2.5.1=py3.12_cuda12.4_cudnn9.1.0_0
  - pytorch-cuda=12.4=hc786d27_7
  - pytorch-mutex=1.0=cuda
  - pyyaml=6.0.2=py312h5eee18b_0
  - pyzmq=25.1.2=py312h6a678d5_0
  - readline=8.2=h5eee18b_0
  - requests=2.32.3=py312h06a4308_1
  - six=1.16.0=pyh6c4a22f_0
  - sqlite=3.45.3=h5eee18b_0
  - stack_data=0.6.2=pyhd8ed1ab_0
  - tbb=2021.8.0=hdb19cb5_0
  - tk=8.6.14=h39e8969_0
  - torchaudio=2.5.1=py312_cu124
  - torchtriton=3.1.0=py312
  - torchvision=0.20.1=py312_cu124
  - tornado=6.4.1=py312h5eee18b_0
  - traitlets=5.14.3=pyhd8ed1ab_0
  - urllib3=2.2.3=py312h06a4308_0
  - wcwidth=0.2.13=pyhd8ed1ab_0
  - xz=5.4.6=h5eee18b_1
  - yaml=0.2.5=h7b6447c_0
  - zeromq=4.3.5=h3b0a872_7
  - zipp=3.21.0=pyhd8ed1ab_0
  - zlib=1.2.13=h5eee18b_1
  - zstd=1.5.6=hc292b87_0
  - pip:
      - absl-py==2.1.0
      - accelerate==1.1.1
      - ace-tools==0.0
      - aiohappyeyeballs==2.4.3
      - aiohttp==3.11.7
      - aiosignal==1.3.1
      - annotated-types==0.7.0
      - antlr4-python3-runtime==4.9.3
      - anyio==4.6.2.post1
      - astunparse==1.6.3
      - attrs==24.2.0
      - audeer==2.2.0
      - audformat==1.3.1
      - audinterface==1.2.2
      - audiofile==1.5.0
      - audioread==3.0.1
      - audmath==1.4.1
      - audobject==0.7.11
      - audresample==1.3.3
      - av==13.1.0
      - be-great==0.0.8
      - beautifulsoup4==4.13.3
      - bitsandbytes==0.44.1
      - bleach==6.2.0
      - blinker==1.9.0
      - blobfile==3.0.0
      - boto3==1.35.68
      - botocore==1.35.68
      - cachetools==5.5.2
      - category-encoders==2.6.4
      - cffi==1.17.1
      - click==8.1.7
      - cloudpickle==3.1.0
      - cmake==3.31.6
      - contourpy==1.3.1
      - copulas==0.12.0
      - ctgan==0.10.2
      - cut-cross-entropy==24.11.4
      - cycler==0.12.1
      - dataclasses-json==0.6.7
      - datasets==3.1.0
      - deepecho==0.6.1
      - deepface==0.0.93
      - defusedxml==0.7.1
      - dill==0.3.8
      - diskcache==5.6.3
      - distlib==0.3.9
      - distro==1.9.0
      - docker-pycreds==0.4.0
      - docstring-parser==0.16
      - einops==0.8.1
      - evaluate==0.4.3
      - faiss-cpu==1.10.0
      - faker==33.0.0
      - fastjsonschema==2.21.1
      - fire==0.7.0
      - flask==3.1.0
      - flask-cors==5.0.1
      - flatbuffers==25.2.10
      - fonttools==4.55.0
      - frozenlist==1.5.0
      - fsspec==2024.9.0
      - gast==0.6.0
      - gdown==5.2.0
      - gensim==4.3.3
      - gitdb==4.0.11
      - gitpython==3.1.43
      - google-pasta==0.2.0
      - greenlet==3.1.1
      - grpcio==1.68.0
      - gunicorn==23.0.0
      - h11==0.14.0
      - h5py==3.13.0
      - hf-transfer==0.1.8
      - httpcore==0.16.3
      - httpx==0.23.3
      - httpx-sse==0.4.0
      - huggingface-hub==0.26.2
      - imageio==2.37.0
      - imbalanced-learn==0.12.4
      - iprogress==0.4
      - ipywidgets==8.1.5
      - iso3166==2.1.1
      - iso639-lang==2.5.1
      - itsdangerous==2.2.0
      - jax==0.5.3
      - jaxlib==0.5.3
      - jieba==0.42.1
      - jiter==0.8.2
      - jmespath==1.0.1
      - joblib==1.4.2
      - jsonpatch==1.33
      - jsonpointer==3.0.0
      - jsonschema==4.23.0
      - jsonschema-specifications==2024.10.1
      - jupyterlab-pygments==0.3.0
      - jupyterlab-widgets==3.0.13
      - kagglehub==0.3.6
      - keras==3.9.2
      - kiwisolver==1.4.7
      - langchain==0.3.19
      - langchain-community==0.3.18
      - langchain-core==0.3.40
      - langchain-experimental==0.3.4
      - langchain-openai==0.2.12
      - langchain-text-splitters==0.3.6
      - langsmith==0.2.3
      - lazy-loader==0.4
      - libclang==18.1.1
      - librosa==0.10.2.post1
      - lightgbm==4.5.0
      - llama-cpp-python==0.3.4
      - llvmlite==0.43.0
      - lxml==5.3.0
      - lz4==4.4.4
      - markdown==3.7
      - markdown-it-py==3.0.0
      - marshmallow==3.26.1
      - matplotlib==3.9.2
      - mdurl==0.1.2
      - mediapipe==0.10.21
      - mistune==3.1.3
      - ml-dtypes==0.5.1
      - msgpack==1.1.0
      - mtcnn==1.0.0
      - mtkresearch==0.3.1
      - multidict==6.1.0
      - multiprocess==0.70.16
      - mypy-extensions==1.0.0
      - namex==0.0.8
      - nbclient==0.10.2
      - nbconvert==7.16.6
      - nbformat==5.10.4
      - ninja==********
      - numba==0.60.0
      - numpy==1.26.4
      - nvidia-nccl-cu12==2.23.4
      - omegaconf==2.3.0
      - openai==1.57.4
      - opencv-contrib-python==*********
      - opencv-python==*********
      - opensmile==2.5.0
      - opt-einsum==3.4.0
      - optree==0.14.1
      - orjson==3.10.12
      - oyaml==1.0
      - pandas==2.2.3
      - pandocfilters==1.5.1
      - patsy==1.0.1
      - peft==0.13.2
      - pip==25.0.1
      - plotly==5.24.1
      - pooch==1.8.2
      - prettytable==3.6.0
      - propcache==0.2.0
      - protobuf==4.25.6
      - psutil==6.1.0
      - pyarrow==18.0.0
      - pycparser==2.22
      - pycryptodomex==3.21.0
      - pydantic==2.10.3
      - pydantic-core==2.27.1
      - pydantic-settings==2.8.1
      - pydub==0.25.1
      - pyjwt==2.8.0
      - pyparsing==3.2.0
      - python-dotenv==1.0.1
      - python-graphviz==0.20.3
      - pytz==2024.2
      - qwen-vl-utils==0.0.8
      - rdt==1.13.1
      - referencing==0.36.2
      - regex==2024.11.6
      - requests-toolbelt==1.0.0
      - retina-face==0.0.17
      - rfc3986==1.5.0
      - rich==13.9.4
      - rpds-py==0.23.1
      - s3transfer==0.10.4
      - safetensors==0.4.5
      - scikit-image==0.25.2
      - scikit-learn==1.5.2
      - scipy==1.13.1
      - sdmetrics==0.17.0
      - sdv==1.17.2
      - seaborn==0.13.2
      - sentence-transformers==3.4.1
      - sentencepiece==0.2.0
      - sentry-sdk==2.19.0
      - setproctitle==1.3.4
      - setuptools==75.8.2
      - shap==0.46.0
      - shtab==1.7.1
      - slicer==0.0.8
      - smart-open==7.1.0
      - smmap==5.0.1
      - sniffio==1.3.1
      - sounddevice==0.5.1
      - soundfile==0.12.1
      - soupsieve==2.6
      - soxr==0.5.0.post1
      - sqlalchemy==2.0.36
      - statsmodels==0.14.4
      - sympy==1.13.1
      - tabgan==2.2.3
      - tenacity==9.0.0
      - tensorboard==2.19.0
      - tensorboard-data-server==0.7.2
      - tensorflow==2.19.0
      - termcolor==3.0.1
      - threadpoolctl==3.5.0
      - tifffile==2025.3.30
      - tiktoken==0.8.0
      - timm==1.0.15
      - tinycss2==1.4.0
      - tokenizers==0.20.3
      - torchao==0.8.0
      - torchtune==0.5.0
      - tqdm==4.67.0
      - transformers==4.46.3
      - trl==0.13.0.dev0
      - typeguard==2.13.3
      - typing-extensions==4.12.2
      - typing-inspect==0.9.0
      - tyro==0.9.1
      - tzdata==2024.2
      - unsloth==2024.11.8
      - unsloth-zoo==2024.11.7
      - virtualenv==20.28.0
      - volcengine-python-sdk==1.0.126
      - wandb==0.18.7
      - webencodings==0.5.1
      - werkzeug==3.1.3
      - wheel==0.45.1
      - widgetsnbextension==4.0.13
      - wordcloud==1.9.4
      - wrapt==1.17.2
      - xformers==0.0.28.post3
      - xgboost==2.1.2
      - xxhash==3.5.0
      - yarl==1.18.0
      - ydata-core==0.7.0
      - ydata-datascience==0.7.0
      - ydata-sdk==1.0.1
      - ydata-synthetic==2.0.0
      - zhipuai==2.1.5.20250106
prefix: /home/<USER>/miniconda3/envs/env41124
