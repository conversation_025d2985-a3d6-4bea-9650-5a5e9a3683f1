#%% md
# Embeddings
#%% md
# Preparation
#%%
# ========== 📦 标准库 ==========
import os  # 操作系统相关功能
import sys  # 系统参数和功能
import re  # 正则表达式
import gzip  # 读写 gzip 文件
import collections  # 常用集合类型
import warnings  # 警告控制
warnings.filterwarnings("ignore")

# 添加环境变量路径
sys.path.append(os.path.abspath("../env"))
sys.path.append(os.path.abspath("../../misc/env"))

# ========== 🧩 环境配置 ==========
from dotenv import load_dotenv  # 加载 .env 环境变量
from config import EnvironmentConfig
from tutorial_config import TutorialEnvironmentConfig

# ========== 🧮 数值计算与数据处理 ==========
import numpy as np  # 数组和数学运算
import pandas as pd  # 数据分析
import torch  # PyTorch 深度学习框架
import torch.nn.functional as F  # PyTorch 函数式接口
from scipy.spatial.distance import cosine  # 计算余弦距离

# ========== 🧠 机器学习 & 特征工程 ==========
from sklearn.datasets import load_digits  # 示例数据
from sklearn.feature_extraction.text import CountVectorizer, TfidfTransformer, TfidfVectorizer  # 文本特征提取
from sklearn.metrics.pairwise import cosine_similarity  # 计算余弦相似度
from sklearn.decomposition import PCA, LatentDirichletAllocation  # 降维与主题建模
from sklearn.manifold import TSNE  # t-SNE 可视化降维

# ========== 💬 自然语言处理 ==========
import jieba  # 中文分词
import gensim  # NLP 处理库
from gensim import corpora, models, similarities  # 语料库与相似度
from gensim.models import Word2Vec, KeyedVectors  # 词向量模型

from transformers import (
    AutoTokenizer,
    AutoModel,
    AutoModelForCausalLM,
    BertTokenizer,
    BertModel
)  # 预训练语言模型和分词器

from sentence_transformers import SentenceTransformer, util  # 句向量模型
from langchain_openai import OpenAIEmbeddings  # LangChain 中的 OpenAI 嵌入

# ========== 📊 可视化 ==========
import matplotlib.pyplot as plt  # 静态图
import plotly.graph_objects as go  # Plotly 图形对象
import plotly.express as px  # 快速可视化
from wordcloud import WordCloud  # 词云图
from IPython.display import display, HTML  # Jupyter 显示支持

# ========== 🔍 相似度/索引工具 ==========
import faiss  # Facebook 提供的相似度搜索库

#%%
device = "cuda:0" if torch.cuda.is_available() else "cpu"
load_dotenv()  # This loads the variables from .env
env_config = TutorialEnvironmentConfig()
#%% md
## Settings
#%%
device = "cuda:0" if torch.cuda.is_available() else "cpu"
load_dotenv()  # This loads the variables from .env
env_config = TutorialEnvironmentConfig()
#%%
mental_disorders = {
    "Depression": "Depressive disorder (also known as depression) is a common mental disorder. It involves a depressed mood or loss of pleasure or interest in activities for long periods of time. Depression is different from regular mood changes and feelings about everyday life.",
    "Axiety" : "Anxiety is a feeling of fear, dread, and uneasiness. It might cause you to sweat, feel restless and tense, and have a rapid heartbeat. It can be a normal reaction to stress. For example, you might feel anxious when faced with a difficult problem at work, before taking a test, or before making an important decision.",
    "bipolar": "Bipolar disorder is a serious mental illness that causes unusual shifts in mood, ranging from extreme highs (mania or “manic” episodes) to lows (depression or “depressive” episode). A person who has bipolar disorder also experiences changes in their energy, thinking, behavior, and sleep.",
    "Schizophrenia": "Schizophrenia usually involves delusions (false beliefs), hallucinations (seeing or hearing things that don't exist), unusual physical behavior, and disorganized thinking and speech. It is common for people with schizophrenia to have paranoid thoughts or hear voices.",
    "Drug addiction": "Drug addiction refers to a chronic disease characterized by the compulsive or uncontrollable urge to seek and use drugs, despite the harmful consequences and changes in the brain, which can be long-lasting. These changes in the brain can lead to the harmful behaviors seen in people who use drugs. Drug addiction is also considered a relapsing disease because people in recovery from drug use are at increased risk for returning to drug use even after years of not taking the drug.",
    "mental health" : "Mental health refers to a state of well-being in which an individual realizes their own abilities, can cope with the normal stresses of life, can work productively and fruitfully, and is able to make a contribution to their community. It encompasses emotional, psychological, and social well-being, and it influences how we think, feel, and act."
}
user_query =  "I am neither depressed nor anxious, and definitely not bipolar."
user_query_2 = "I love gambling and medicine."
#%%
mental_disorders_chinese = {
    "抑郁症": "抑郁症（也称为抑郁障碍）是一种常见的精神障碍。它涉及到持续时间长的抑郁情绪或对活动失去乐趣或兴趣。抑郁症与常规的情绪变化和对日常生活的感受不同。",
    "焦虑症": "焦虑是一种恐惧、恐慌和不安的感觉。它可能导致你出汗、感到焦虑和紧张，以及心跳加速。它可以是对压力的正常反应。例如，在面对工作中的难题、在参加考试前或在做重要决定前，你可能会感到焦虑。",
    "双相情感障碍": "双相情感障碍是一种严重的精神疾病，导致情绪的异常波动，范围从极高（躁狂发作或“躁狂”期）到极低（抑郁发作或“抑郁”期）。患有双相情感障碍的人还会经历他们的能量、思维、行为和睡眠的变化。",
    "精神分裂症" : "精神分裂症通常涉及妄想（错误的信念）、幻觉（看到或听到不存在的东西）、不寻常的身体行为以及思维和语言的混乱。患有精神分裂症的人通常会有偏执的思想或听到声音。",
    "药物成瘾" : "药物成瘾指的是一种慢性疾病，其特征为强迫性或无法控制的寻求和使用药物的冲动，尽管这样做会带来有害后果并且可能导致长期的大脑改变。这些大脑中的改变能够导致使用药物的人出现有害行为。药物成瘾也被认为是一种复发性疾病，因为即使在停止使用药物多年之后，从药物使用中恢复的人重新开始使用药物的风险也会增加。",
    "心理健康" : "心理健康指的是一种幸福状态，个体能认识到自己的能力，能够应对生活中的正常压力，能够高效和富有成效地工作，并能够为社区做出贡献。它包括情感、心理和社会福祉，并影响我们的思考、感受和行为方式。"
}
user_query_chinese = "我不抑郁，不焦虑，也绝对不是双相情感障碍。"
query_new_chinese = "我赌博"
#%%
# 停用词设定
stop_words=['\n', 'or', 'are', 'they', 'i', 'some', 'by', '—', 'was','from','which',
            'even', 'the', 'to', 'a', 'and', 'of', 'in', 'on', 'for', 
            'that', 'with', 'is', 'as', 'could', 'its', 'this', 'other',
            'an', 'have', 'more', 'at','don’t', 'can', 'only', 'most']

# 定义停用词列表
stop_words_chinese = ['的', '了', '在', '是', '我','）','（','。','，','、']
#%% md
## Functions
#%%
from enum import Enum
import openai  # OpenAI SDK，用于调用 OpenAI API

# 📌 定义 AI 供应商（Provider）
class APISource(Enum):
    """AI 供应商枚举"""
    OPENAI = "openai"  # OpenAI API
    DEEPSEEK = "deepseek"  # DeepSeek API
    ZHIPUAI = "zhipuai" 
    VOLCANO = "volcano" # 豆包
    ALIYUN_D = "aliyun_domestic" # 阿里云 境内
    ALIYUN_E = "aliyun_external" # 阿里云 境外
    AWS = "aws"  # AWS Bedrock API
    HUGGINGFACE = "huggingface"

# 📌 定义 AI 模型（Model）
class APIModel(Enum):
    
    """AI 模型枚举，包括 OpenAI、DeepSeek 和 AWS"""
    # OpenAI 模型
    GPT_4O = ("gpt-4o", APISource.OPENAI)
    GPT_4O_MINI = ("gpt-4o-mini", APISource.OPENAI)
    GPT_35_TURBO = ("gpt-3.5-turbo", APISource.OPENAI)
    
    # DeepSeek 模型
    DEEPSEEK_CHAT = ("deepseek-chat", APISource.DEEPSEEK)
    DEEPSEEK_REASONER = ("deepseek-reasoner", APISource.DEEPSEEK)
    DEEPSEEK_CODER = ("deepseek-coder", APISource.DEEPSEEK)
    
    
    # AWS Bedrock 模型
    # ref: https://docs.aws.amazon.com/bedrock/latest/userguide/models-supported.html
    # ref: https://github.com/langchain-ai/langchain-aws/issues/258 for “on-demand throughput isn’t supported.”
    AWS_LLAMA_3_2_3B = ("us.meta.llama3-2-3b-instruct-v1:0", APISource.AWS)

    # Huggingface 模型
    HF_MS_DIALOGPT_MEDIUM =  ("microsoft/DialoGPT-medium", APISource.HUGGINGFACE)  

    # zhipu 模型
    ZHIPUAI_GLM_4_PLUS =  ("glm-4-plus", APISource.ZHIPUAI) 

    # volcano
    VOL_DOUBAO_1_5_LITE = ("doubao-1.5-lite-32k-250115", APISource.VOLCANO) 
    VOL_DOUBAO_1_5_PRO = ("doubao-1.5-pro-32k-250115", APISource.VOLCANO) 

    # aliyun
    ALI_D_QWEN_PLUS = ("qwen-plus-2025-01-25", APISource.ALIYUN_D) 
    ALI_D_QWEN_MAX = ("qwen-max-2025-01-25", APISource.ALIYUN_D) 

    def __init__(self, model_name, provider):
        """
        初始化模型枚举
        :param model_name: 模型名称（如 "gpt-4o"）
        :param provider: 供应商（Source.OPENAI / Source.DEEPSEEK / Source.AWS）
        """
        self.model_name = model_name  # 存储模型名称
        self.provider = provider  # 存储供应商信息

# 📌 定义 AI 供应商（Provider）
class HGModelType(Enum):
    """AI 供应商枚举"""
    SENTENCE_TRANSFORMER = "sentence_transformer"
    AUTO_MODEL = "auto_model"
    CAUSAL_MODEL = "causal_model"
    CAUSAL_MODEL_INS = "causal_model_tuned_for_instruction"

# 📌 定义 AI 模型（Model）
class HGModel(Enum):

    # OpenAI 模型
    GPT2 = ("gpt2", HGModelType.CAUSAL_MODEL)
    TINY_GPT2 = ("sshleifer/tiny-gpt2", HGModelType.CAUSAL_MODEL)
    BERT_BASE_NLI_MEAN = ("sentence-transformers/bert-base-nli-mean-tokens", HGModelType.SENTENCE_TRANSFORMER)
    ALL_MINILM_L6_V2 = ("sentence-transformers/all-MiniLM-L6-v2", HGModelType.SENTENCE_TRANSFORMER)
    LLAMA_3_2_1B_INS = ("meta-llama/Llama-3.2-1B-Instruct", HGModelType.CAUSAL_MODEL_INS)
    LLAMA_3_2_1B = ("meta-llama/Llama-3.2-1B", HGModelType.CAUSAL_MODEL)
    DISTILGPT2 = ("distilgpt2", HGModelType.CAUSAL_MODEL)


    def __init__(self, model_name, model_type):
        """
        初始化模型枚举
        :param model_name: 模型名称（如 "gpt-4o"）
        :param provider: 供应商（Source.OPENAI / Source.DEEPSEEK / Source.AWS）
        """
        self.model_name = model_name  # 存储模型名称
        self.model_type = model_type  # 存储供应商信息

class ModelHelper():

    def __init__(self, env_config : EnvironmentConfig):
        self.env_config = env_config
        pass
    
    def show_3D_embedding_dict(self, data:dict, use_key_for_value = False):
        """
        输入一个 dict，key 作为标签，value 是句子，将其嵌入并用 3D PCA 可视化。
        
        参数:
            data (dict): key 为显示用的 label，value 为句子文字。
        """
        if not isinstance(data, dict):
            raise ValueError("请传入一个字典，格式为 {label: sentence}")

        # 拆解 label 和对应句子
        display_labels = list(data.keys())
        sentence_list = list(data.values())
        if use_key_for_value:
            sentence_list = list(data.keys())
        

        # 产生 embedding 向量
        embeddings = [self.generate_sentence_embedding(sentence)[0].cpu().detach().numpy() for sentence in sentence_list]
        array_embedding = np.array(embeddings)
        embedding_tensor = torch.from_numpy(array_embedding).float()

        # 使用 PCA 降维至 3 维
        pca = PCA(n_components=3)
        reduced_embeddings = pca.fit_transform(embedding_tensor.cpu().numpy())

        # 建立 DataFrame 用来画图
        df = pd.DataFrame(reduced_embeddings, columns=["PC1", "PC2", "PC3"])
        df["element"] = display_labels

        # 使用 Plotly 画 3D 散点图
        fig = px.scatter_3d(df, x="PC1", y="PC2", z="PC3", text="element",
                            title="3D Visualization of Sentence Embeddings",
                            labels={"PC1": "主成分 1", "PC2": "主成分 2", "PC3": "主成分 3"},
                            opacity=0.8)

        fig.update_layout(
            width=800,
            height=600,
            margin=dict(l=50, r=50, b=50, t=50)
        )

        fig.update_traces(marker=dict(size=8, opacity=0.8))
        fig.show()
            
    def show_3D_embedding(self, sentence_list, display_labels = []):

        if len(display_labels) != len(sentence_list):
            display_labels = sentence_list

        # embedding_mix  = self.model.encode(word_list, convert_to_tensor=True)
        embeddings = [self.generate_sentence_embedding(single_sentence) for single_sentence in sentence_list]
        array_embedding = np.array(embeddings)
        embedding_mix = torch.from_numpy(array_embedding).float()
        
        # Reduce dimensions to 3D using PCA
        pca = PCA(n_components=3)
        reduced_embeddings = pca.fit_transform(embedding_mix.cpu().numpy())

        # Convert to Pandas DataFrame for visualization
        df = pd.DataFrame(reduced_embeddings, columns=["PC1", "PC2", "PC3"])
        df["element"] = display_labels

        # Create interactive 3D scatter plot
        fig = px.scatter_3d(df, x="PC1", y="PC2", z="PC3", text="element",
                            title="3D Visualization of Sentence Embeddings",
                            labels={"PC1": "Principal Component 1", "PC2": "Principal Component 2", "PC3": "Principal Component 3"},
                            opacity=0.8)

        # Enlarge the figure size
        fig.update_layout(
            width=800,  # Set width
            height=600,  # Set height
            margin=dict(l=50, r=50, b=50, t=50)  # Adjust margins
        )

        fig.update_traces(marker=dict(size=8, opacity=0.8))
        fig.show()

        

class APIModelHelper(ModelHelper):

    def __init__(self, model: APIModel, env_config : EnvironmentConfig):
        """
        初始化 AI 客户端
        :param model: 选择的 AI 模型（如 Model.GPT_4O, Model.CLAUDE_V2）
        """
        super().__init__(env_config)
        self.model = model  # 存储模型
        self.provider = model.provider  # 获取供应商（OpenAI / DeepSeek / AWS）

        # 📌 根据供应商配置 API 访问
        if self.provider == APISource.OPENAI:
            self.api_key = os.getenv("openai_api_key")  # 请替换为您的 OpenAI API 密钥
            self.base_url = None  # OpenAI 默认 API 端点
            self.client = openai.OpenAI(api_key=self.api_key)
        else:
            raise ValueError("❌ 无效的供应商，请使用 Model 枚举选择模型！")

        pass

    def generate_sentence_embedding(self, sentence):

        embedding_result = ""

        response = self.client.embeddings.create(
            input=sentence,
            model=self.model_id  # "text-embedding-ada-002" generates 1536-dimension embeddings
        )

        embedding = response.data[0].embedding  # Extract embedding vector
        embedding_result =  np.array(embedding)  # Convert to NumPy array

        return embedding_result
        
class HGModelHelper(ModelHelper):

    """
    A helper class to prepare and preprocess data for machine learning tasks.
    Includes functionalities for missing value handling, outlier removal, scaling, feature selection, and more.
    """

    def __init__(self, hg_model: HGModel , env_config : EnvironmentConfig,  model_type: HGModelType = HGModelType.SENTENCE_TRANSFORMER,
                 given_model = None, given_tokenizer = None):
        """
        Initialize the DataPreparationHelper with a DataFrame.

        Parameters:
        - df (pd.DataFrame): The input data to be preprocessed.
        """
        super().__init__(env_config)
        
        if given_model is not None and given_tokenizer is not None: 
            self.model = given_model
            self.model_id = hg_model.model_name
            self.model_type = model_type
            self.tokenizer = given_tokenizer
        else:
            self.hg_model = hg_model
            self.model_id = hg_model.model_name
            self.model_type = self.hg_model.model_type
            self.huggingface_cache_location = env_config.path_huggingface_cache
            
            if self.model_type == HGModelType.SENTENCE_TRANSFORMER:
                self.model = SentenceTransformer(self.model_id,cache_folder=self.huggingface_cache_location,trust_remote_code=True)

            if self.model_type == HGModelType.AUTO_MODEL:
                # 加载对应的 tokenizer 和模型
                if self.huggingface_cache_location == "":
                    
                    self.tokenizer = AutoTokenizer.from_pretrained(self.model_id)
                else:
                    self.tokenizer = AutoTokenizer.from_pretrained(self.model_id, 
                                        cache_dir=self.huggingface_cache_location,  # 指定缓存目录
                                        trust_remote_code=True)
                    
                if self.huggingface_cache_location == "":
                    self.model = AutoModel.from_pretrained(self.model_id)
                else:
                    self.model = AutoModel.from_pretrained(self.model_id, 
                                                cache_dir=self.huggingface_cache_location, 
                                                trust_remote_code=True)             
                self.model.eval()  # 设为评估模式

                # 如果有 GPU 可用，使用 GPU
                device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
                self.model.to(device)

            if self.model_type == HGModelType.CAUSAL_MODEL or self.model_type == HGModelType.CAUSAL_MODEL_INS:
                # 加载对应的 tokenizer 和模型
                if self.huggingface_cache_location == "":
                    
                    self.tokenizer = AutoTokenizer.from_pretrained(self.model_id)
                else:
                    self.tokenizer = AutoTokenizer.from_pretrained(self.model_id, 
                                        cache_dir=self.huggingface_cache_location,  # 指定缓存目录
                                        trust_remote_code=True)
                    
                if self.huggingface_cache_location == "":
                    self.model = AutoModelForCausalLM.from_pretrained(self.model_id)
                else:
                    self.model = AutoModelForCausalLM.from_pretrained(self.model_id, 
                                                cache_dir=self.huggingface_cache_location, 
                                                output_hidden_states=True,
                                                trust_remote_code=True)             
                self.model.eval()  # 设为评估模式

                # 如果有 GPU 可用，使用 GPU
                device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
                self.model.to(device)
    
    def generate_sentence_embedding(self, sentence):

        embedding_result = ""

        if self.model_type == HGModelType.SENTENCE_TRANSFORMER :
            # 使用 SentenceTransformer 计算句子嵌入，并进行 L2 归一化（默认行为）
            embedding_result = self.model.encode(sentence, normalize_embeddings=True)


        if self.model_type == HGModelType.AUTO_MODEL:

            # 将输入句子进行分词，转换为模型可以接受的格式
            tokens = self.tokenizer(sentence, 
                            padding=True,        # 进行填充（确保 batch 形状一致）
                            truncation=True,     # 进行截断（防止超长输入）
                            return_tensors="pt") # 以 PyTorch 张量的形式返回
            
            # ---------------------------
            # 通过 AutoModel 计算句子嵌入
            # ---------------------------

            # 禁用梯度计算（避免额外的内存消耗，提高推理效率）
            with torch.no_grad():
                output = self.model(**tokens)  # 前向传播获取模型输出

            # 获取 token 级别的嵌入表示
            token_embeddings = output.last_hidden_state  # 形状: [batch_size, token_count, hidden_dim]

            # ---------------------------
            # 手动实现 Mean Pooling（SentenceTransformer 默认使用的池化方法）
            # ---------------------------

            # 获取 attention_mask，值为 1 的位置表示有效 token，值为 0 的位置表示 padding
            attention_mask = tokens["attention_mask"]

            # 扩展维度，使其与 token_embeddings 的形状匹配
            expanded_mask = attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()

            # 计算均值池化，即对 token 嵌入进行加权求和，并除以有效 token 数
            mean_pooled_embedding = torch.sum(token_embeddings * expanded_mask, dim=1) / expanded_mask.sum(dim=1)

            # ---------------------------
            # 进行 L2 归一化（确保嵌入向量的模长为 1）
            # ---------------------------
            mean_pooled_embedding = torch.nn.functional.normalize(mean_pooled_embedding, p=2, dim=1)

            # 将张量转换为 NumPy 数组，以便与 SentenceTransformer 的结果进行比较
            embedding_result = mean_pooled_embedding.cpu().numpy()[0]
    
        if self.model_type in [HGModelType.CAUSAL_MODEL,HGModelType.CAUSAL_MODEL_INS] :

            outputs = self.get_model_output(sentence)
            hidden_states = outputs.hidden_states       # Tuple: (embed, layer1, ..., layerN)
            final_hidden = hidden_states[-1]            # Shape: (1, seq_len, hidden_dim)
            embedding_result = final_hidden[:, -1, :]   # (1, hidden_dim)

         
        return embedding_result


    # 获取从词 A 到词 B 的概念向量
    def get_concept_vector(self, sentence_a, sentence_b):
        concept_vector = self.generate_sentence_embedding(sentence_b) - self.generate_sentence_embedding(sentence_a)
        return concept_vector

    def generate_response(self, user_input, max_new_tokens=500):
        """
        Generates a response from the chatbot using Llama-3.2-3B-Instruct.

        :param user_input: The input text from the user.
        :param max_new_tokens: The maximum number of tokens to generate.
        :return: The chatbot's response.
        """
        prompt = ""
        if self.model_type == HGModelType.CAUSAL_MODEL_INS:
            # Prepare conversation format
            messages = [
                {"role": "system", "content": "You are a helpful AI assistant."},
                {"role": "user", "content": user_input},
            ]

            # Convert messages to chat template format
            prompt = self.tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)

        if self.model_type == HGModelType.CAUSAL_MODEL:
            prompt = user_input

        # Set pad_token_id if it's missing
        if self.tokenizer.pad_token_id is None:
            self.tokenizer.pad_token_id = self.tokenizer.eos_token_id or self.tokenizer.convert_tokens_to_ids("<pad>")

        # Set eos_token_id if it's missing (you may need to define one manually)
        if self.tokenizer.eos_token_id is None:
            self.tokenizer.eos_token_id = self.tokenizer.pad_token_id  # fallback — not ideal but avoids warnings

        # Tokenize input
        inputs = self.tokenizer(prompt, return_tensors="pt")
        input_ids = inputs["input_ids"].to(self.model.device)
        attention_mask = inputs["attention_mask"].to(self.model.device)
        # Generate response
        with torch.no_grad():
            output_ids = self.model.generate(
                input_ids=input_ids,
                attention_mask=attention_mask,  # ✅ FIXED
                max_new_tokens=max_new_tokens,
                pad_token_id= self.tokenizer.pad_token_id,
                eos_token_id= self.tokenizer.eos_token_id,
                do_sample=False,
                temperature=1,  # Adjust for randomness (0 = deterministic)
                top_k=50,         # Filter out low probability words
                top_p=0.95,       # Nucleus sampling
            )

        # Decode output
        generated_text = self.tokenizer.decode(output_ids[0], skip_special_tokens=False)

        # Extract the assistant's response
        response = generated_text.split("<|start_header_id|>assistant<|end_header_id|>")[-1].replace("<|eot_id|>", "").strip()
        response = response.split("<|begin_of_text|>")[-1]
        return response



    def generate_next_word_from_sentence_by_embedding(self, input_text):
        generated_tokens = []
        max_steps = 10
        current_text = input_text.strip()

        for _ in range(max_steps):
            embeddings = self.generate_sentence_embedding(input_text)
            decoded_token = self.generate_next_token_from_embeddings(embeddings)
            # Stop when a word boundary is detected
            if decoded_token in [" ", ".", ",", "!", "?", "\n"]:
                break

            generated_tokens.append(decoded_token)
            # Append to current text
            current_text += decoded_token

        print("".join(generated_tokens).strip())

    def generate_next_token_from_sentence_by_embedding(self, input_text):
        embeddings = self.generate_sentence_embedding(input_text)
        return self.generate_next_token_from_embeddings(embeddings)


    def generate_next_token_from_embeddings(self, embeddings):

        # Project to logits and get top token
        logits = self.model.lm_head(embeddings)  # shape: (1, vocab_size)
        next_token_id = torch.argmax(logits, dim=-1)  # shape: (1,)

        # Decode the token
        decoded_token = self.tokenizer.decode(next_token_id.item())

        return decoded_token

    def generate_top_k_predictions(self, sentence, k=5, verbose = False):
        """
        Generate top-k predicted tokens from an embedding using cosine similarity to the input embedding table.
        
        Args:
            embeddings (torch.Tensor): Tensor of shape (hidden_dim,)
            k (int): Number of top predictions to return
        
        Returns:
            List[Tuple[str, float]]: List of (token, probability) pairs
        """
        embeddings = self.generate_sentence_embedding(sentence)
        
        return self.generate_top_k_predictions_by_embeddings(embeddings, k = k, verbose=verbose)


    def generate_top_k_predictions_by_embeddings(self, embeddings, k=5, verbose = False):

        logits = self.model.lm_head(embeddings)  # shape: (1, vocab_size)

        if verbose:
            print("logits:\n\n{logits}")

        # Compute probabilities
        probs = F.softmax(logits, dim=-1)  # shape: (1, vocab_size)

        # Get top-k token IDs and probabilities
        topk = torch.topk(probs, k, dim=-1)
        topk_ids = topk.indices[0]        # shape: (k,)
        topk_probs = topk.values[0]       # shape: (k,)

        # Decode tokens and pair with probabilities
        top_k_tokens = []
        for token_id, prob in zip(topk_ids, topk_probs):
            token_str = self.tokenizer.decode([token_id.item()])
            top_k_tokens.append((token_str, prob.item()))

        if verbose:
            for token, prob in top_k_tokens:
                print(f"Token: '{token}'\tProbability: {prob:.4f}")

        return top_k_tokens

    def get_model_output(self, sentence):

        outputs = ""
        if not self.model_type in [HGModelType.CAUSAL_MODEL, HGModelType.CAUSAL_MODEL_INS]:
            print("It is not a generative model.")
            return ""


        if self.model_type == HGModelType.CAUSAL_MODEL:
            # 将输入句子进行分词，转换为模型可以接受的格式
            inputs = self.tokenizer(sentence, return_tensors="pt").to(device)
            with torch.no_grad():
                outputs = self.model(**inputs, output_hidden_states=True)

        if self.model_type == HGModelType.CAUSAL_MODEL_INS:
            # Prepare conversation format
            messages = [
                {"role": "system", "content": "You are a helpful AI assistant."},
                {"role": "user", "content": sentence},
            ]

            # Convert messages to chat template format
            prompt = self.tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)


            # 将输入句子进行分词，转换为模型可以接受的格式
            inputs = self.tokenizer(prompt, return_tensors="pt").to(device)
            with torch.no_grad():
                # outputs = self.model(**inputs, output_hidden_states=True)
                outputs = self.model(
                    **inputs,
                    # max_new_tokens=500,
                    pad_token_id= self.tokenizer.pad_token_id,
                    eos_token_id= self.tokenizer.eos_token_id,
                    temperature=0.7,  # Adjust for randomness (0 = deterministic)
                    top_k=50,         # Filter out low probability words
                    top_p=0.95,       # Nucleus sampling,
                    output_hidden_states=True
                )
        return outputs



    def top_k_predictions_of_concept_modified_sentence(self, sentence, concept_vector, k = 5):
        current_embeddings = self.generate_sentence_embedding(sentence)
        return self.top_k_predictions_of_concept_modified_embedding(current_embeddings, concept_vector, k=k)
    
    def top_k_predictions_of_concepts_modified_sentence(self, sentence, list_concept_vector: list, multiplier = 1,  k = 5):
        current_embeddings = self.generate_sentence_embedding(sentence)
        return self.top_k_predictions_of_concepts_modified_embedding(current_embeddings, list_concept_vector, multiplier, k = k)


    def top_k_predictions_of_concept_modified_embedding(self, current_embeddings, concept_vector, k = 5):

        dic_prediction_original = self.generate_top_k_predictions_by_embeddings(current_embeddings, k=k)
        print(f"original top{k} predictions:\n {dic_prediction_original}")

        new_embeddings = current_embeddings + concept_vector
        dic_prediction_modified = self.generate_top_k_predictions_by_embeddings(new_embeddings, k=k)
        print(f"modified top{k} predictions:\n {dic_prediction_modified}")
        return dic_prediction_modified
    
    def top_k_predictions_of_concepts_modified_embedding(self, current_embeddings, list_concept_vector: list, multiplier = 1,  k = 5):
        from statistics import mean

        dic_prediction_original = self.generate_top_k_predictions_by_embeddings(current_embeddings, k=k)
        print(f"original top{k} predictions:\n {dic_prediction_original}")

        # Assume list_concept_vector is a list of tensors of the same shape
        # Stack them to create a new tensor and compute the mean across the 0th dimension
        mean_concept_vector = torch.mean(torch.stack(list_concept_vector), dim=0)
        
        # Now you can do element-wise addition and scaling
        new_embeddings = current_embeddings + multiplier * mean_concept_vector

        # new_embeddings = current_embeddings + multiplier * mean_concept_vector
        dic_prediction_modified = self.generate_top_k_predictions_by_embeddings(new_embeddings, k=k)
        print(f"modified top{k} predictions:\n {dic_prediction_modified}")
        return dic_prediction_modified
#%%
def average_vectors(vectors):
    if not vectors:
        return []

    # Number of vectors
    num_vectors = len(vectors)
    
    # Length of each vector
    vector_length = len(vectors[0])
    
    # Initialize a zero vector
    avg_vector = [0] * vector_length

    # Sum all vectors component-wise
    for vector in vectors:
        for i in range(vector_length):
            avg_vector[i] += vector[i]

    # Divide each component by the number of vectors
    avg_vector = [x / num_vectors for x in avg_vector]
    
    return avg_vector

# Example usage:
vectors = [[1, 2, 3], [4, 5, 6], [7, 8, 9]]
average = average_vectors(vectors)
print("Average vector:", average)
#%% md
# 再次取得BERT类型Embedding
#%% md
## Bert-Base-Nli
#%%
model_id = "sentence-transformers/bert-base-nli-mean-tokens"   
model_bert_base_ni = SentenceTransformer(model_id,cache_folder= env_config.path_huggingface_cache,trust_remote_code=True)
tokenizer_bert_base_ni  = AutoTokenizer.from_pretrained(model_id,cache_folder= env_config.path_huggingface_cache,trust_remote_code=True)
#%%
embedding_helper_bert = HGModelHelper(hg_model=HGModel.BERT_BASE_NLI_MEAN, given_model=model_bert_base_ni, given_tokenizer= tokenizer_bert_base_ni, env_config = env_config)
#%%
list_mix = ['He is a good student.','She is a good student.','king','queen','The good student is him.','The good student is her.']
embedding_helper_bert.show_3D_embedding(list_mix)
#%% md
# Next Token Generation
#%% md
### Distilled GPT2
#%%
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

# 載入 tokenizer 和 model
model_id_distilgpt2 = "distilgpt2"
tokenizer_distilgpt2 = AutoTokenizer.from_pretrained(model_id_distilgpt2, 
                                          cache_dir=env_config.path_huggingface_cache,  # 指定缓存目录
                                          trust_remote_code=True)

model_tyny_distilgpt2 = AutoModelForCausalLM.from_pretrained(model_id_distilgpt2, 
                                  cache_dir=env_config.path_huggingface_cache, 
                                  output_hidden_states=True,
                                  trust_remote_code=True)
#%%
# 輸入文字
input_text = "I work in a hospital as a"
inputs = tokenizer_distilgpt2(input_text, return_tensors="pt")

# 生成下一個字（token）
with torch.no_grad():
    output = model_tyny_distilgpt2(**inputs)
    logits = output.logits


# Get the logits for the last token
last_token_logits = logits[0, -1, :]

# Predict the next token (highest probability)
predicted_token_id = torch.argmax(last_token_logits).item()
predicted_token = tokenizer_distilgpt2.decode(predicted_token_id)

# 解碼成文字
print(f"Next predicted token: {predicted_token}")

#%% md
### Distilled GPT2 (Hidden Layers)
#%%
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

# 前向傳播，取得 hidden states
with torch.no_grad():
    outputs = model_tyny_distilgpt2(**inputs, output_hidden_states=True)
    hidden_states = outputs.hidden_states  # List: (embedding, layer1, layer2, ..., layerN)
#%%
# 列出每層 embedding 最後一個 token 的向量
print("Per-layer last token embedding shape:")
for i, layer in enumerate(hidden_states):
    print(f"Layer {i}: {layer.shape}")  # shape = (1, seq_len, hidden_size)
    last_token_embedding = layer[0, -1, :]
    print(f"token embedding (first 5 dims): {last_token_embedding[:5]}")
#%%
# 用最後一層的 embedding 預測下一 token 的 logits
final_hidden = hidden_states[-1]  # 最後一層
last_token_embedding = final_hidden[:, -1, :]  # (1, hidden_size)
logits = model_tyny_distilgpt2.lm_head(last_token_embedding)  # (1, vocab_size)
predicted_token_id = torch.argmax(logits, dim=-1).item()
predicted_token = tokenizer_distilgpt2.decode(predicted_token_id)
print(f"\nPredicted next token using final layer embedding: {predicted_token}")

#%%
# Step 3: Get the **embedding vector of the predicted word/token**
# GPT2 shares input and output embeddings — this is the input embedding matrix
embedding_table = model_tyny_distilgpt2.transformer.wte.weight  # (vocab_size, hidden_size)
predicted_word_embedding = embedding_table[predicted_token_id]  # (hidden_size,)

# Print it (or use it downstream)
print(f"\nPredicted token embedding vector (shape {predicted_word_embedding.shape}):")
print(predicted_word_embedding[:10])  # just the first 10 dims
#%% md
### Distilled GPT2 (HGModelHelper)
#%%
model_helper_distill_gpt2 = HGModelHelper(hg_model=HGModel.DISTILGPT2 , env_config = env_config)
#%%
input_text = "I work in a hospital as a"
embeddings = model_helper_distill_gpt2.generate_sentence_embedding(input_text)
embeddings[0,0:3]
#%%
model_helper_distill_gpt2.generate_next_token_from_embeddings(embeddings)
#%%
model_helper_distill_gpt2.generate_top_k_predictions_by_embeddings(embeddings)
#%% md
### Queen Example
#%%
sentence_male = "male,female,"
sentence_female = "female,male,"
sentence_king = "king, king, king,"
sentence_queen = "queen, queen, queen,"
sentence_husband = "When two are married. One is wife and the other is"
sentence_wife = "When two are married. One is husband and the other is"
#%%
model_helper_distill_gpt2.generate_top_k_predictions("queen,king,", k=5)
#%%
to_draw = {}
to_draw["queen"] = sentence_queen
to_draw["king"] = sentence_king
to_draw["female"] = sentence_female
to_draw["male"] = sentence_male
to_draw["husband"] = sentence_husband
to_draw["wife"] = sentence_wife
model_helper_distill_gpt2.show_3D_embedding_dict(to_draw)
#%% md
### Same Word Comparison
#%%
model_helper_distill_gpt2.generate_top_k_predictions("I work in a hospital as a", k=5)
#%%
model_helper_distill_gpt2.generate_top_k_predictions("nurse, nurse, nurse,", k=5)
#%%
model_helper_distill_gpt2.generate_top_k_predictions("The symptom of a patient is checked by a", k=5)
#%%
model_helper_distill_gpt2.generate_top_k_predictions("Nurses work in a", k=5)
#%%
to_draw = {}
to_draw["nurse_repeat"] = "nurse, nurse, nurse,"
to_draw["nurse_hospital"] = "I work in a hospital as a"
to_draw["doctor"] = "The symptom of a patient is checked by a"
to_draw["hospital"] = "Nurses work in a"
model_helper_distill_gpt2.show_3D_embedding_dict(to_draw)
#%% md
### LLAMA_3_2_1B
#%%
model_helper_llama_3_2_1B = HGModelHelper(hg_model=HGModel.LLAMA_3_2_1B , env_config = env_config)
#%%
model_helper_llama_3_2_1B.generate_top_k_predictions("I work in a hospital as a")
#%%
model_helper_llama_3_2_1B.generate_top_k_predictions("How are you")
#%% md
### LLAMA_3_2_1B_INS 
#%%
model_helper_llama_3_2_1B_ins = HGModelHelper(hg_model=HGModel.LLAMA_3_2_1B_INS , env_config = env_config)
#%%
input_text = "How are you?"
model_helper_llama_3_2_1B_ins.generate_response(input_text, max_new_tokens=100)
#%%
embeddings = model_helper_llama_3_2_1B_ins.generate_sentence_embedding(input_text)
model_helper_llama_3_2_1B_ins.generate_next_token_from_embeddings(embeddings)
#%%
model_helper_llama_3_2_1B_ins.generate_response("I work in a hospital as a")
#%%
prediction = model_helper_llama_3_2_1B_ins.generate_top_k_predictions("How are you?")
#%%
model_helper_llama_3_2_1B_ins.generate_response("How are you?")
#%%
prediction = model_helper_llama_3_2_1B_ins.generate_top_k_predictions("Nurses work in a")
#%%
model_helper_llama_3_2_1B_ins.generate_response("Nurses work in a")
#%%
model_helper_llama_3_2_1B_ins.generate_response("Repeat one word after me. Nurse, Nurse, Nurse.", max_new_tokens = 100)
#%%
model_helper_llama_3_2_1B_ins.generate_response("Answer with a single word. If I work in a hospital. I am most likely to be a?", max_new_tokens = 100)
#%%
model_helper_llama_3_2_1B_ins.generate_response("Answer with a single word. If I work in a hospital and I am respected. I am most likely to be a?", max_new_tokens = 100)
#%%
to_draw = {}
to_draw["nurse_repeat"] = "Repeat one word after me. Nurse, Nurse, Nurse."
to_draw["nurse_hospital"] = "Answer with a single word. If I work in a hospital. I am most likely to be a?"
to_draw["doctor"] = "Answer with a single word. If I work in a hospital and I am respected. I am most likely to be a?"
model_helper_llama_3_2_1B_ins.show_3D_embedding_dict(to_draw)
#%% md
#### Cultural
#%%
sentence_conflict_1 ="""
                如果你是一个西方人。
                请阅读以下两句谚语，告诉我你更认同哪一句？
                A：至深之爱生于孤独。
                B：爱非真即伪，无中间立场。
                只回答A或B。
            """
model_helper_llama_3_2_1B_ins.generate_response(input_text, max_new_tokens=100)
#%%
model_helper_llama_3_2_1B_ins.generate_top_k_predictions(input_text)
#%%
sentence_conflict_2 ="""
                如果你是一个东方人。
                请阅读以下两句谚语，告诉我你更认同哪一句？
                A：柔弱胜刚强。
                B：领袖绝不可示弱。
                只回答A或B。
            """
model_helper_llama_3_2_1B_ins.generate_top_k_predictions(sentence_conflict_2)
#%%
sentence_conflict_2 ="""
                三位母亲都有女儿。每位母亲都有自己的一套价值观，并以此为指导努力抚养女儿。
                现在女儿们都长大了，每个人都开始摒弃母亲的许多价值观。假如是你，你认为该如何解决这个冲突？
                请在以下两个选项中选取你认为最合适的一项。
                A：双方应各退一步，寻找兼顾双方需求的中间方案。
                B：应确认哪一方是正确的，并强制执行其主张。
                只回答A或B，只回答一个字。
            """
model_helper_llama_3_2_1B_ins.generate_top_k_predictions(sentence_conflict_2)
#%%
sentence_conflict_2 ="""
                三位母亲都有女儿。每位母亲都有自己的一套价值观，并以此为指导努力抚养女儿。
                现在女儿们都长大了，每个人都开始摒弃母亲的许多价值观。假如是你，你认为该如何解决这个冲突？
                请在以下两个选项中选取你认为最合适的一项。
                A：双方应各退一步，寻找兼顾双方需求的中间方案。
                B：应确认哪一方是正确的，并强制执行其主张。
                只回答A或B，只回答一个字。
            """
model_helper_llama_3_2_1B_ins.generate_top_k_predictions(sentence_conflict_2)
#%%
model_helper_llama_3_2_1B_ins.generate_top_k_predictions(sentence_conflict_2)
#%%
model_helper_llama_3_2_1B_ins.show_3D_embedding_dict(to_draw)
#%%
model_helper_llama_3_2_1B_ins.generate_response("领袖绝不可示弱。")
#%%
to_draw = {}
to_draw["A"] = "Always answer me one word 'yes'. 柔弱胜刚强。"
to_draw["B"] = "Always answer me one word 'yes'. 领袖绝不可示弱。"
to_draw["Chinese"] = "Repeat after me: Chinese"
to_draw["American"] = "Repeat after me: American"
to_draw["Russian"] = "Repeat after me: Russian"
model_helper_llama_3_2_1B_ins.show_3D_embedding_dict(to_draw)
#%% md
# Model Intervention
#%% md
### Distilled GPT2
#%%
model_helper_distill_gpt2 = HGModelHelper(hg_model=HGModel.DISTILGPT2 , env_config = env_config)
#%%
sentence_male = "male, male, male, male, male, male,"
sentence_female = "female, female, female, female, female, female,"
sentence_king = "king, king, king, king, king, king,"
sentence_queen = "queen, queen, queen, queen, queen, queen,"
sentence_father = "father, father, father, father, father, father,"
sentence_mother = "mother, mother, mother, mother, mother, mother,"
sentence_husband = "husband, husband, husband, husband, husband, husband,"
sentence_wife = "wife, wife ,wife, wife, wife, wife,"
sentence_leader = "leader, leader, leader, leader, leader, leader,"
sentence_supporter = "supporter, supporter, supporter, supporter, supporter, supporter,"
#%%
model_helper_distill_gpt2.generate_next_token_from_sentence_by_embedding(sentence_queen)
#%%
to_draw = {}
to_draw["male"] = sentence_male
to_draw["female"] = sentence_female
to_draw["father"] = sentence_father
to_draw["mother"] = sentence_mother
to_draw["king"] = sentence_king
to_draw["queen"] = sentence_queen
to_draw["husband"] = sentence_husband
to_draw["wife"] = sentence_wife
model_helper_distill_gpt2.show_3D_embedding_dict(to_draw)
#%%
# 获取概念向量
concept_from_female_to_male = model_helper_distill_gpt2.get_concept_vector(sentence_female, sentence_male)
concept_from_wife_to_husband = model_helper_distill_gpt2.get_concept_vector(sentence_wife, sentence_husband)
concept_from_mother_to_father = model_helper_distill_gpt2.get_concept_vector(sentence_mother, sentence_father)
multiplier = 2
#%%
model_helper_distill_gpt2.top_k_predictions_of_concept_modified_sentence(sentence_queen, multiplier * concept_from_female_to_male)
#%%
model_helper_distill_gpt2.top_k_predictions_of_concept_modified_sentence(sentence_queen, multiplier * concept_from_wife_to_husband)
#%%
model_helper_distill_gpt2.top_k_predictions_of_concept_modified_sentence(sentence_queen, 0.5*(concept_from_wife_to_husband + concept_from_female_to_male))
#%%
model_helper_distill_gpt2.top_k_predictions_of_concepts_modified_sentence(sentence_queen, 
    [concept_from_wife_to_husband, concept_from_female_to_male, concept_from_mother_to_father], multiplier=2)
#%%
sentence_enemy = "Children are not"
model_helper_distill_gpt2.generate_next_token_from_sentence_by_embedding(sentence_enemy)
#%%
model_helper_distill_gpt2.top_k_predictions_of_concepts_modified_sentence(sentence_enemy, 
    [concept_from_wife_to_husband, concept_from_female_to_male, concept_from_mother_to_father], multiplier=1)
#%% md
#### Cultural
#%%
sentence_male = "male,female,"
sentence_female = "female,male,"
sentence_king = "king, king, king,"
sentence_queen = "queen, queen, queen,"
sentence_husband = "When two are married. One is wife and the other is"
sentence_wife = "When two are married. One is husband and the other is"
#%%
model_helper_distill_gpt2.top_k_predictions_of_concepts_modified_sentence(sentence_queen, 
    [concept_from_wife_to_husband, concept_from_female_to_male, concept_from_mother_to_father], multiplier=1)
#%% md
### LLAMA_3_2_1B
#%%
sentence_male = "I am either a female or a"
sentence_female = "I am either a male or a"
#%%
model_helper_llama_3_2_1B.generate_response(sentence_male, max_new_tokens= 30)
#%%
model_helper_llama_3_2_1B.generate_next_word_from_sentence_by_embedding(sentence_male)
#%%
# 获取概念向量
concept_from_female_to_male = model_helper_llama_3_2_1B.get_concept_vector(sentence_female, sentence_male)
concept_from_female_to_male
#%%
sentence_nurse = "nurse, nurse, nurse,"
model_helper_llama_3_2_1B.generate_next_word_from_sentence_by_embedding(sentence_nurse)
#%%
sentence_male_repeated = "male,male,male,"
model_helper_llama_3_2_1B.generate_next_word_from_sentence_by_embedding(sentence_male_repeated)
#%%
key_for_comparison = {}
key_for_comparison["nurse"] = sentence_nurse
key_for_comparison["female"] = sentence_female
key_for_comparison["male"] = sentence_male
key_for_comparison["repeated male"] = sentence_male_repeated

model_helper_llama_3_2_1B.show_3D_embedding_dict(key_for_comparison)
#%%
model_helper_llama_3_2_1B.top_k_predictions_of_concept_modified_sentence(sentence_nurse, concept_from_female_to_male)
#%% md
### LLAMA_3_2_1B_INS
#%%
sentence_male_ins = "Answer with a single word. I am not a female, so I am a ?"
sentence_female_ins = "Answer with a single word. I am not a male, so I am a ?"
#%%
model_helper_llama_3_2_1B.generate_response(sentence_female, max_new_tokens = 100)
#%%
# 获取概念向量
concept_from_female_to_male = model_helper_llama_3_2_1B_ins.get_concept_vector(sentence_female, sentence_male)
concept_from_female_to_male
#%%
sentence_doctor = "Answer with a single word. If I work in a hospital and I am respected. I am most likely to be a?"
#%%
sentence_nurse = "Repeat one word after me. nurse, nurse, nurse."
#%%
model_helper_llama_3_2_1B_ins.generate_response(sentence_nurse)
#%%
embeddings_male = model_helper_llama_3_2_1B_ins.generate_sentence_embedding(sentence_male)
embeddings_male
#%%
model_helper_llama_3_2_1B_ins.generate_next_token_from_embeddings(embeddings_male)
#%%
embeddings_female = model_helper_llama_3_2_1B_ins.generate_sentence_embedding(sentence_female)
model_helper_llama_3_2_1B_ins.generate_next_token_from_embeddings(embeddings_female)
#%%
sentence_queen = "One word. Who is the wife of a king?"
model_helper_llama_3_2_1B_ins.generate_next_word_from_sentence_by_embedding(sentence_queen)
#%%
embeddings_nurse = model_helper_llama_3_2_1B_ins.generate_sentence_embedding(sentence_nurse)
model_helper_llama_3_2_1B_ins.generate_top_k_predictions_by_embeddings(embeddings_nurse, k = 5)
#%%
model_helper_llama_3_2_1B_ins.top_k_predictions_of_concept_modified_sentence(sentence_queen, concept_from_female_to_male)
#%%
model_helper_llama_3_2_1B_ins.top_k_predictions_of_concept_modified_sentence(sentence_queen, 1*concept_from_female_to_male)
#%% md
#### American vs Chinese
#%%
sentence_conflict_2 ="""
                三位母亲都有女儿。每位母亲都有自己的一套价值观，并以此为指导努力抚养女儿。
                现在女儿们都长大了，每个人都开始摒弃母亲的许多价值观。假如是你，你认为该如何解决这个冲突？
                请在以下两个选项中选取你认为最合适的一项。
                A：双方应各退一步，寻找兼顾双方需求的中间方案。
                B：应确认哪一方是正确的，并强制执行其主张。
                只回答A或B，只回答一个字。
            """
model_helper_llama_3_2_1B_ins.generate_top_k_predictions(sentence_conflict_2)
#%%
sentence_Chinese = "Repeat after me: Chinese"
sentence_American = "Repeat after me: American"
sentence_Russian = "Repeat after me: Russian"
#%%
concept_from_American_to_Chinese = model_helper_distill_gpt2.get_concept_vector(sentence_American, sentence_Chinese)
#%%
model_helper_llama_3_2_1B_ins.top_k_predictions_of_concept_modified_sentence(sentence_conflict_2, concept_from_American_to_Chinese, k = 5)
#%% md
# 附录
- 版权：Ivan Liu 最后更新时间 2024
- 虚拟环境：env41124
- 内容来源（本教学的部分内容改编自）
    - 
- 参考文献 

