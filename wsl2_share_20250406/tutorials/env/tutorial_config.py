import os
import sys
sys.path.append(os.path.abspath("../../misc/env"))  # path to the folder containing environment_config.py
from config import EnvironmentConfig
from dotenv import load_dotenv
load_dotenv()

class TutorialEnvironmentConfig(EnvironmentConfig):
    def __init__(self):
        env_keys = [
            "path_project_root",
            "path_data_cache",
            "path_huggingface_cache",
            "path_datasets_cache",
            "path_gguf_cache",
            "path_openml_cache",
            "path_others_cache",
            "path_results",
            "path_resources",
            "path_resources_external",
            "api_key_hf",
            "api_key_wb",
            "api_key_baidu_key",
            "api_key_baidu_secret",
            "api_key_openai",
            "api_key_deep_seek",
            "api_key_zhipuai",
            "api_key_volcano",
            "api_key_aliyun_domestic",
            "api_key_aliyun_external",
            "api_key_aws_key",
            "api_key_aws_secret"
        ]

        for key in env_keys:
            setattr(self, key, os.getenv(key, ""))
        
        path_root = os.getenv("path_project_root")
        path_local_resources = os.path.join(path_root, "tutorials", "resources")
        path_local_images = os.path.join(path_local_resources, "images")
        path_raschka_images = os.path.join(path_local_images, "raschka_machine_learning")
        path_nb_images = os.path.join(path_local_images, "nb")

        setattr(self, 'path_local_resources', path_local_resources)
        setattr(self, 'path_local_images', path_local_images)
        setattr(self, 'path_raschka_images', path_raschka_images)
        setattr(self, 'path_nb_images', path_nb_images)
        