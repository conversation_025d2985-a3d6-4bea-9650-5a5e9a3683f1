from enum import Enum

class TutorialPathType(Enum):
    ProjectRoot = "project_root"

class TutorialPaths():
    def ProjectRoot

    def __init__(self):
        pass
    


# Usage
model = ModelConfig()
print(model.OPENAI_API_KEY)
print(model.MODEL_NAME)

# 📌 AI 聊天客户端
class TutorialEnvSettings:
    """
    AI 聊天客户端，支持 OpenAI、DeepSeek 和 AWS Bedrock。
    支持：
    ✅ 逐词流式输出
    ✅ 完整返回模式
    """

    def __init__(self, model: Model):
        pass