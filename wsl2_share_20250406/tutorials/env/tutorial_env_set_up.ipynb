#%% md
# Windows Second System
#%% md
# Remote Linux Server
#%% md
# Conda

`Conda` 是一個開源的包管理與環境管理系統，主要用於 Python 和其他語言的軟件包管理。它可以幫助用戶安裝、更新、管理軟件包，以及創建、克隆和管理虛擬環境。

## Conda 安裝 (Conda Installation)

### 1. 下載與安裝 Miniconda 或 Anaconda

`Conda` 主要透過以下兩種方式進行安裝：

- **Anaconda**：包含 Python 及大量科學計算與數據分析的庫，適合需要完整軟件生態的用戶。
- **Miniconda**：只包含 `conda` 和 Python 的基本環境，更適合需要最小安裝的用戶。

### 2. 下載安裝包

#### Windows 用戶：
可前往 [Anaconda 官網](https://www.anaconda.com/) 下載對應的安裝程序，並按照指示安裝。

#### macOS / Linux 用戶：
可使用終端下載並安裝 Miniconda：
```bash
# 下載 Miniconda 安裝包（以 Linux x86_64 為例）
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh

# 執行安裝腳本
bash Miniconda3-latest-Linux-x86_64.sh

# 重新啟動終端以啟用 conda
source ~/.bashrc

#%% md
# Pip