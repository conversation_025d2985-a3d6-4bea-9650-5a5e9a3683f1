#%% md
# A Finger on the Pulse of Happiness
#%% md
## Preparation

### Prepare Environment
#%%
# Importing necessary libraries
import pandas as pd  # For data manipulation and analysis
import numpy as np  # For numerical operations and data transformations
import matplotlib.pyplot as plt  # For plotting graphs and visualizations
import seaborn as sns  # For advanced data visualization (using seaborn built on top of matplotlib)
# import utilities as util  # Assuming this is a custom module for additional utility functions
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
from sklearn.tree import DecisionTreeRegressor
from sklearn.ensemble import RandomForestRegressor, BaggingRegressor
from statsmodels.formula.api import ols
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.svm import SVR
from sklearn.model_selection import KFold
from sklearn.neural_network import MLPRegressor
import shap
import sys
import os
import psychai.statistics.data_preparation.normalization as norm
import psychai.statistics.data_preparation.outlier as outlier
import psychai.statistics.linear_regression.linear_regression as lr
import psychai.machine_learning.regression_supervise_learning as rsl
# import psychai.statistics.bland_altman_analysis.bland_altman_analysis as baa

do_correlation_analysis = True  # Flag to enable correlation analysis
do_regression_analysis = True  # Flag to enable regression analysis
do_machine_learning = True  # Flag to enable machine learning tasks
do_stepwise_regression = False  # Flag to enable stepwise regression analysis
do_categorical_machine_learning = True  # Flag to enable categorical ML analysis
try_transformation = True  # Flag to enable transformation of variables (for model improvement or scaling)
plot_significant_only = False  # Plot only significant features/variables
plot_all = False  # Plot all variables regardless of significance
print_summary = True  # Print a summary of the results or analysis
do_processed_output = True  # Output processed data
do_descriptive_statistics = True  # Calculate and print descriptive statistics

root_file_path = os.getcwd()

# ---- Constants ----
# These are fixed values that define thresholds, limits, and parameters for the analysis.

# Total number of combinations for some kind of data analysis or model training
num_combinations = 14 * 23 * 3

# Adjust the significance level using Bonferroni correction (to control for multiple comparisons)
# This ensures more stringent criteria when considering a variable as statistically significant.
significant_level = 0.05 / num_combinations  

# Upper bound for model validation threshold
model_valid_upper_bound = 2

# The minimum percentage of valid waveform data required (for quality control purposes)
valid_waveform_percentage_lower_bound = 0.8  # Originally set at 0.8 (can be adjusted)

# Minimum data points per heartbeat for valid signal analysis (ensuring data quality)
data_points_per_heartbeat_lower_bound = 15  # Originally set at 15

# Minimum length of PPG (Photoplethysmogram) data for a valid analysis
ppg_length_lower_bound = 100

# Upper limit for the age of participants (ensuring that analysis is within a reasonable age range)
age_upper_bound = 100

# Gender filter (for models or analysis that differentiate by gender)
# Can be "M" for male, "F" for female, or "Both" to include all genders in the analysis.
gender = "Both"  

# Outlier multiplier (used to detect and remove extreme values in data, typically in boxplot-based analysis)
k = 1.5  # Common multiplier for detecting outliers using the IQR method

save_path = os.path.join(root_file_path,"results")
#%% md
### Prepare Data
#%%
# Function to read data
def read_data(file_name):
    full_path = os.path.join(root_file_path,"data",file_name)
    return pd.read_csv(full_path, sep="\t")

# Read data
data = read_data("data.txt")
data.columns = data.columns.str.replace('.', '_', regex=False)
print(len(data.columns))
#%%
# ---- Data Filtering ----

# Drop rows where any of the key columns contain NaN values (i.e., missing data).
# This ensures we have complete data for essential variables used in the analysis.
# Columns dropped if NaN: 'Valid_Model_Percentage', 'ReflectionIndex', 'ValidWaveformPercentage',
# 'PPG_Heart_Rate', 'SWLS', 'The_Subjective_Vitality_Scale', 'GAD_7', 'PHQ_9', 'PANAS_P'
data_complete = data.dropna(subset=[
    'Valid_Model_Percentage', 'ReflectionIndex', 'ValidWaveformPercentage', 
    'PPG_Heart_Rate', 'SWLS', 'The_Subjective_Vitality_Scale', 
    'GAD_7', 'PHQ_9', 'PANAS_P'
])

# ---- Creating Gender-Specific Datasets ----
# Create separate datasets for males (M) and females (F) based on the 'Gender' column.
# This allows for gender-specific analysis later on.

# Filter the data to include only male participants
data_complete_m = data_complete[data_complete['Gender'] == "M"]

# Filter the data to include only female participants
data_complete_f = data_complete[data_complete['Gender'] == "F"]

# ---- Calculating Average Age and Standard Deviation for Each Gender ----
# This provides summary statistics of the participants' age by gender for comparison.

# Calculate the mean age of male participants
complete_average_age_m = data_complete_m['Age'].mean()

# Calculate the mean age of female participants
complete_average_age_f = data_complete_f['Age'].mean()

# Calculate the standard deviation of age for male participants (a measure of age variation)
complete_std_age_m = data_complete_m['Age'].std()

# Calculate the standard deviation of age for female participants
complete_std_age_f = data_complete_f['Age'].std()

# ---- Further Data Filtering ----

# Create a copy of the complete dataset to apply further filtering based on quality control measures.
data_valid = data_complete.copy()

# 1. Filter out rows where the PPG (Photoplethysmogram) length is below the threshold.
# This ensures that only participants with enough data points are considered for further analysis.
data_valid = data_valid[data_valid['PPG_Length'] > ppg_length_lower_bound]

# 2. Filter out rows where the number of data points per heartbeat is below the lower bound.
# This ensures valid heartbeat measurements for each participant.
data_valid = data_valid[data_valid['Data_Points_Per_Heartbeat'] > data_points_per_heartbeat_lower_bound]

# 3. Filter out rows where the percentage of valid waveforms is below the specified threshold.
# This quality control step ensures that only high-quality waveform data is used.
data_valid = data_valid[data_valid['ValidWaveformPercentage'] > valid_waveform_percentage_lower_bound]

# ---- Removing Rows with Missing PSD (Power Spectral Density) Values ----
# The 'psd_columns' list contains the names of the columns for PSD values.
# These columns must not have NaN values (i.e., missing data), so we drop any rows where one or more are missing.
# PSD is often used in analyzing signals (e.g., heart rate, PPG), so complete data is essential for this analysis.

psd_columns = ['PSD_0', 'PSD_1', 'PSD_2', 'PSD_3', 'PSD_4', 'PSD_5', 'PSD_6']

# Drop rows where any of the PSD columns contain NaN values.
data_valid = data_valid.dropna(subset=psd_columns)

#%%
# Rename columns
data_valid['HR'] = data_valid['HR_Average']
data_valid['Vitality'] = data_valid['The_Subjective_Vitality_Scale'] 

# rename
data_valid.rename(columns = {'ReflectionIndex':'RI'}, inplace = True) 
data_valid.rename(columns = {'EstimatedReflectionIndex':'E_RI'}, inplace = True) 
data_valid.rename(columns = {'AdaptedReflectionIndex':'A_RI'}, inplace = True) 

# duplicate
data_valid['PPT'] = data_valid['FirstPeakToSecondPeakTime'] 
data_valid['CT'] = data_valid['ValleyToFirstPeakTime'] 

# Calculating various other parameters

data_valid['NT'] = data_valid['ValleyToFirstPeakTime'] + data_valid['FirstPeakToNotchTime']
data_valid['DT'] = data_valid['FirstPeakToSecondPeakTime'] + data_valid['SecondPeakToValleyTime']
data_valid['A1'] = data_valid['FirstPeakHeight'] * data_valid['ValleyToFirstPeakTime'] / 2 + (data_valid['FirstPeakHeight'] + data_valid['NotchHeight']) * data_valid['FirstPeakToNotchTime'] / 2
data_valid['A2'] = data_valid['NotchHeight'] * data_valid['NotchToValleyTime'] / 2
data_valid['IPA'] = data_valid['A2'] / data_valid['A1']
data_valid['RCA'] = data_valid['ValleyToFirstPeakTime'] / data_valid['NT']
data_valid['RDA'] = data_valid['FirstPeakToNotchTime'] / (data_valid['ValleyToFirstPeakTime'] + data_valid['FirstPeakToSecondPeakTime'] + data_valid['SecondPeakToValleyTime'])

data_valid['SI'] = data_valid['Height'] / data_valid['FirstPeakToSecondPeakTime']

# Calculating curvature features
data_valid['B_A'] = abs(data_valid['FirstNegativeSecondDerivative']/data_valid['FirstPositiveSecondDerivative'])
data_valid['E_A'] = abs(data_valid['SecondPositiveSecondDerivative']/data_valid['FirstPositiveSecondDerivative'])
data_valid['F_A'] = abs(data_valid['SecondNegativeSecondDerivative']/data_valid['FirstPositiveSecondDerivative'])
data_valid['G_A'] = abs(data_valid['ThirdPositiveSecondDerivative']/data_valid['FirstPositiveSecondDerivative'])
data_valid['H_A'] = abs(data_valid['ThirdNegativeSecondDerivative']/data_valid['FirstPositiveSecondDerivative'])
data_valid = data_valid.drop('C_A', axis=1)
data_valid = data_valid.drop('D_A', axis=1)

# Calculating PSD and ratios
psd_cols = ['PSD_1', 'PSD_2', 'PSD_3', 'PSD_4', 'PSD_5', 'PSD_6']
data_valid['PSD_All'] = data_valid[psd_cols].sum(axis=1)
for col in psd_cols:
    data_valid[f'r_{col}'] = data_valid[col] / data_valid['PSD_All']

data_valid['NHA'] = 1 - data_valid['PSD_1']**2 / data_valid[psd_cols].pow(2).sum(axis=1)
data_valid['IHAR'] = (1 - data_valid['NHA']) / data_valid['IPA']
#%%

# Assuming 'data_valid' is your DataFrame
normalized_by_ppg = ['B_A', 'E_A', 'F_A','G_A', 'H_A', "RI", "E_RI", "A_RI", "PPT", "SI", "CT", "NT", "DT", "IPA", "RCA", "RDA", "r_PSD_1", "r_PSD_2", "r_PSD_3", "r_PSD_4", "r_PSD_5", "r_PSD_6", "NHA", "IHAR"]

# Normalize by PPG and HR
data_valid = norm.normalize_and_add_by_variables(data_valid, normalized_by_ppg, "PPG_Heart_Rate", 75, False)
#%%
# Calculate Aging Index and normalize it
data_valid['Aging_Index'] = np.abs(data_valid['B_A']) - np.abs(data_valid['E_A'])
data_valid, _ = norm.normalize_and_add_by_variable(data_valid, 'Aging_Index', 'PPG_Heart_Rate', 75)

# Apply logarithmic transformations
for col in ['B_A_75', 'E_A_75', 'F_A_75','G_A_75', 'H_A_75']:
    log_col_name = f'{col}_Log'
    data_valid[log_col_name] = np.log(data_valid[col] - data_valid[col].min() + 0.01)
#%%
# Variable lists
power_spectrum_densities_2_75 = ["r_PSD_1_75", "r_PSD_2_75", "r_PSD_3_75", "r_PSD_4_75", "r_PSD_5_75", "r_PSD_6_75"]
acceleration_75 = ["B_A_75", "E_A_75", "F_A_75", "G_A_75", "H_A_75", "NHA_75", "IHAR_75"]
waveform_75 = ["RI_75", "E_RI_75", "A_RI_75", "PPT_75", "SI_75", "Aging_Index_75", "CT_75", "NT_75", "DT_75", "IPA_75", "RCA_75", "RDA_75"]
normalized_by_height = waveform_75 + acceleration_75 + power_spectrum_densities_2_75

# Normalize the dataset
data_valid = norm.normalize_and_add_by_variables(data_valid, normalized_by_height, "Height", 170)
#%%
# List of variables to apply logarithmic transformation
log_transform_variables = [
    'B_A', 'G_A', 'H_A', 'E_A', 'F_A'
]

# Apply logarithmic transformation and append as new columns
for var in log_transform_variables:
    transformed_var = f'{var}_Log'
    data_valid[transformed_var] = np.log(data_valid[var] - data_valid[var].min() + 0.1)


# List of PSD variables to apply logarithmic transformation
psd_variables = [
    'r_PSD_1', 'r_PSD_2', 'r_PSD_3',
    'r_PSD_4', 'r_PSD_5', 'r_PSD_6'
]

# Apply logarithmic transformation and append as new columns
for var in psd_variables:
    transformed_var = f'{var}_Log'
    data_valid[transformed_var] = np.log(data_valid[var] - data_valid[var].min() + 0.1)
#%%

# Apply logarithmic transformation and append as new columns
data_valid['NHA_Log'] = np.log(data_valid['NHA'] - data_valid['NHA'].min() + 0.1)
data_valid['IHAR_Log'] = np.log(data_valid['IHAR'] - data_valid['IHAR'].min() + 0.1)
data_valid['SI_75_Log'] = np.log(data_valid['SI_75'] - data_valid['SI_75'].min() + 0.1)
data_valid['SI_75_170_Log'] = np.log(data_valid['SI_75_170'] - data_valid['SI_75_170'].min() + 0.1)

# Save the DataFrame to a file
data_valid.to_csv(os.path.join(save_path,"Data-1-Valid.txt"), sep="\t", index=False)

import pandas as pd

# Assuming 'data_valid' is a pandas DataFrame
# and 'normalized_to_dataset' is defined as previously discussed

# Defining variable lists
pulse_waveform_variables = ["FirstPeakToSecondPeakTime", "ReflectionIndex", "EstimatedReflectionIndex", "RI_75", "E_RI_75", "SI", "SI_75"]
power_spectrum_densities = ["Relative_PSD_1", "Relative_PSD_2", "Relative_PSD_3", "Relative_PSD_4", "Relative_PSD_5", "Relative_PSD_6"]
power_spectrum_densities_2 = ["r_PSD_1", "r_PSD_2", "r_PSD_3", "r_PSD_4", "r_PSD_5", "r_PSD_6", "NHA", "IHAR"]
power_spectrum_densities_75 = ["Relative_PSD_1_75", "Relative_PSD_2_75", "Relative_PSD_3_75", "Relative_PSD_4_75", "Relative_PSD_5_75", "Relative_PSD_6_75"]
acceleration = ["B_A", "C_A", "D_A", "E_A", "F_A"]
acceleration_75_Log = ["B_A_75_Log", "C_A_75_Log", "D_A_75_Log", "E_A_75_Log", "F_A_75_Log"]
acceleration_75_170_Log = ["B_A_75_170_Log", "C_A_75_170_Log", "D_A_75_170_Log", "E_A_75_170_Log", "F_A_75_170_Log"]
HRV = ["rMSSD_1", "pNN50_1", "SDNN_1", "HF1_1", "LF1_1"]

# Normalize HRV variables
data_valid = norm.normalize_and_add_by_variables(data_valid, HRV, "PPG_Heart_Rate", 75)

# Defining variable lists
controlled_variables = ["Height", "HR"]
psychological_variables = ["PHQ_9", "GAD_7", "UCLA", "PANAS_P", "PANAS_N", "SWLS","Vitality", "Extraversion", "Agreeableness", "Conscientiousness", "Stability", "Openness",  "V", "A"]
dependent_variables = psychological_variables  # Assuming psychological_variables is already defined
independent_variables_raw = ["PPT", "CT", "IPA", "RCA", "RDA", "SI", "RI", "E_RI", "B_A", "E_A", "F_A", "G_A", "H_A", "Aging_Index"] + power_spectrum_densities_2
independent_variables_75 = ["PPT_75", "CT_75", "DT_75", "NT_75", "IPA_75", "RCA_75", "RDA_75", "SI_75", "RI_75", "E_RI_75", "Aging_Index_75"] + acceleration_75 + power_spectrum_densities_2_75
power_spectrum_densities_2_75_170_height = ["r_PSD_1_75_170", "r_PSD_2_75_170", "r_PSD_3_75_170", "r_PSD_4_75_170", "r_PSD_5_75_170", "r_PSD_6_75_170"]
power_spectrum_densities_2_75_170_height_Log = ["r_PSD_1_75_170_Log", "r_PSD_2_75_170_Log", "r_PSD_3_75_170_Log", "r_PSD_4_75_170_Log", "r_PSD_5_75_170_Log", "r_PSD_6_75_170_Log", "NHA_75_170_Log", "IHAR_75_170_Log"]
acceleration_75_170_height = ["B_A_75_170", "E_A_75_170", "F_A_75_170","G_A_75_170", "H_A_75_170",  "NHA_75_170", "IHAR_75_170"]
waveform_75_170_height = ["RI_75_170", "E_RI_75_170", "A_RI_75_170", "PPT_75_170", "Aging_Index_75_170", "CT_75_170", "NT_75_170", "DT_75_170", "IPA_75_170", "RCA_75_170", "RDA_75_170"]
independent_variables = ["SI_75"]  + waveform_75_170_height + acceleration_75_170_height + power_spectrum_densities_2_75_170_height
#%% md
### Descpritive Analysis
#%%
# Function to display descriptive statistics for participants
def get_descriptive_data(df_input):
    """
    This function calculates and prints basic descriptive statistics for participants in a dataset.
    It handles unique participants, gender distribution, and age statistics.
    
    Args:
        df_input (DataFrame): The input DataFrame containing participant data.

    Returns:
        None
    """

    # Ensure each participant appears only once by removing duplicate entries based on 'Participant_SN'.
    unique_participants = df_input.drop_duplicates(subset='Participant_SN')
    
    # Print the total number of unique participants.
    num_participants = len(unique_participants)
    print(f"Number of participants: {num_participants}")

    # ---- Gender Statistics ----
    
    # Calculate the number of male participants.
    male_participants = len(unique_participants[unique_participants['Gender'] == 'M'])
    
    # Calculate the percentage of male participants.
    male_percentage = (male_participants / num_participants) * 100
    
    # Print the percentage of male participants.
    print(f"Percentage of male participants: {male_percentage:.2f}%")
    
    # ---- Age Statistics ----
    
    # Calculate and print the average (mean) age of the participants.
    average_age = unique_participants['Age'].mean()
    print(f"Average age: {average_age:.2f}")
    
    # Calculate and print the standard deviation of age (measuring variability in participant ages).
    age_std = unique_participants['Age'].std()
    print(f"Standard deviation of age: {age_std:.2f}")

#%%
get_descriptive_data(data)
print('====')
get_descriptive_data(data_complete)
print('====')
get_descriptive_data(data_valid)
#%% md
## Descriptive Statistics
#%%
if do_descriptive_statistics:
    # Defining the list of variables for descriptive statistics
    descriptive_statistics = ["Height", "Age", "Gender_1"] + controlled_variables + dependent_variables + independent_variables

    # Grouping data by 'Participant_SN' and calculating the mean for each variable
    descriptive_statistics_table = data_valid.groupby('Participant_SN')[descriptive_statistics].mean().reset_index()

    # Renaming 'Participant_SN' column
    descriptive_statistics_table.rename(columns={'Participant_SN': 'ParticipantSN'}, inplace=True)

    # Save the descriptive statistics table to a file
    descriptive_statistics_table.to_csv(os.path.join(save_path,"Description_Analysis.txt"), sep=",", index=False)

    # Print results
    print(descriptive_statistics_table)

#%%
#part 16
if do_processed_output:
    # Starting with ID and ValidWaveformPercentage
    data_output = data_valid[['ID', 'ValidWaveformPercentage']]

    # Adding columns from various lists
    for var_list in [independent_variables, dependent_variables, controlled_variables, psychological_variables]:
        for var in var_list:
            if var in data_valid.columns:
                data_output[var] = data_valid[var]

    # Save the output DataFrame to a file
    
    data_output.to_csv(os.path.join(save_path,"processed_data_median_Valid_Larger_Than_0.8.csv"), sep=",", index=False)

#%% md
## Correlation Analysis
#%%
# Font size for the chart elements
font_size = 10

# List of variable names
independent_variables = [
    'E_RI_75_170',
    'SI_75',
    'CT_75_170',
    'IPA_75_170',
    'RCA_75_170',
    'PPT_75_170',
    'Aging_Index_75_170',
    'NT_75_170',
    'DT_75_170',
    'RDA_75_170',
    'B_A_75_170',
    'E_A_75_170',
    'F_A_75_170',
    'G_A_75_170',
    'H_A_75_170',
    'r_PSD_1_75_170',
    'r_PSD_2_75_170',
    'r_PSD_3_75_170',
    'r_PSD_4_75_170',
    'r_PSD_5_75_170',
    'r_PSD_6_75_170',
    'NHA_75_170',
    'IHAR_75_170',
]

# Corresponding display names for the chart
display_names = [
    'RI',
    'SI',
    'CT',
    'IPA',
    'RCA',
    'PPT',
    'AI',
    'NT',
    'DT',
    'RDA',
    'B/A',
    'E/A',
    'F/A',
    'G/A',
    'H/A',
    'PSD1',
    'PSD2',
    'PSD3',
    'PSD4',
    'PSD5',
    'PSD6',
    'NHA',
    'IHAR',
]

if do_correlation_analysis:
    mean_sd_table = []
    all_variables = independent_variables

    for i, var in enumerate(all_variables):
        display_name = display_names[i]
        with_outliers = data_valid[var]
        without_outliers = outlier.remove_outliers(data_valid, var, k)[var]

        with_outlier_stats = [display_name, with_outliers.mean(), with_outliers.std(), len(with_outliers)]
        without_outlier_stats = [without_outliers.mean(), without_outliers.std(), len(without_outliers)]

        mean_sd_table.append(with_outlier_stats + without_outlier_stats)

    columns = ["Variable", "WithOutlierMean", "WithOutlierSD", "WithOutlierN", "WithoutOutlierMean", "WithoutOutlierSD", "WithoutOutlierN"]
    mean_sd_df = pd.DataFrame(mean_sd_table, columns=columns)

    # Calculating correlation matrix
    data_cor_table = data_valid[all_variables].corr(method="pearson")

    # Replace variable names with display names for correlation matrix
    data_cor_table.columns = display_names
    data_cor_table.index = display_names

    # Resetting index to avoid issues
    data_cor_table.reset_index(drop=True, inplace=True)
    mean_sd_df.reset_index(drop=True, inplace=True)

    # Create a list of numbers for the x-axis
    x_labels = list(range(1, len(display_names) + 1))

    # Custom annotation function to remove leading zero for 0.xx and convert 1.00 to 1
    def custom_annot(val):
        if val == 1.00:
            return '1'
        else:
            return f'{val:.2f}'.replace("0.", ".")

    # Displaying the correlation matrix heatmap with annotations
    plt.figure(figsize=(12, 10))

    # Create the heatmap with font size adjustments
    ax = sns.heatmap(data_cor_table, cmap='coolwarm', center=0, linewidths=.5, annot=True, fmt=".2f",
                     annot_kws={"size": font_size},  # Annotation font size
                     yticklabels=display_names, xticklabels=x_labels, cbar_kws={'label': 'Correlation'})

    # Apply custom formatting to the annotations
    for t in ax.texts:
        t.set_text(custom_annot(float(t.get_text())))

    # Set font size for the title and labels
    plt.xlabel('Variable Number', fontsize=font_size)
    plt.ylabel('Variable Name', fontsize=font_size)
    plt.title('Correlation Coefficient Matrix', fontsize=font_size)

    # Set font size for the ticks (x and y axis labels)
    plt.xticks(fontsize=font_size)
    plt.yticks(fontsize=font_size)

    # Adjust font size for the color bar (legend bar)
    colorbar = ax.collections[0].colorbar
    colorbar.ax.tick_params(labelsize=font_size)  # Set the font size of the colorbar ticks
    colorbar.set_label('Correlation', fontsize=font_size)  # Set the font size of the colorbar label

    # Display the heatmap with annotations
    plt.show()

    # Concatenating the correlation matrix and the mean-SD DataFrame
    save_result = pd.concat([data_cor_table, mean_sd_df], axis=1)

    # Saving the result to a file
    full_path = os.path.join(save_path,"correlation_matrix.txt")
    save_result.to_csv(full_path, sep="\t", index=False)

#%% md
## Linear Regression
#%%

if do_regression_analysis:
    result_df, largest_r_squared_df = lr.explore_simple_linear_regression_all(data_valid, dependent_variables, independent_variables, k, try_transformation, plot_all, plot_significant_only, print_summary, significant_level)

    # Save the results to a file
    result_df.to_csv(os.path.join(save_path,"Result.txt"), sep="\t", index=False)
    largest_r_squared_df.to_csv(os.path.join(save_path,"largest_r_squared_Result.txt"), sep="\t", index=False)

#%% md
## Machine Learning
#%%
prediction_methods = ["Random Forest"]
dependent_variables = psychological_variables
data_valid_name_limited = data_valid.copy()
data_valid_name_replaced = data_valid_name_limited[independent_variables+dependent_variables]
data_valid_name_replaced.rename(columns=dict(zip(independent_variables, display_names)), inplace=True)

if do_machine_learning:
    rsl.regression_supervised_learning(data_valid_name_replaced, prediction_methods,dependent_variables,display_names,(50,20))
#%% md
# Footnotes
- Copyright：Ivan Liu.
- Last updated: 2024/10/11
- Anaconda Environment：course_4
- Reference:
    - None