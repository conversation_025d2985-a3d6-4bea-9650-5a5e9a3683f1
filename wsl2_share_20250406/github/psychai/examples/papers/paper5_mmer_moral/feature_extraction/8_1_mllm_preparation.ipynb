#%% md
# Feature Extraction - Data Preparation for LLM
#%%
import cv2
import os

# Path to the main folder containing subfolders (001 to 120)
# main_folder_path = r"E:\Backup\Experiments\Moral Elevation\Disk1_2_combined\FS-9 Day 1"
main_folder_path = r"G:\Backup\Experiments\Emotional_Exhausion\PS-11\Results"
# Path to the folder where you want to save extracted frames
output_folder_path = r"C:\Working\Programming\Python\psychai_v1\example\paper5_mmer_moral\results\mmllm\frame_output_20241114"
#%%
import os
import cv2

# Create the output folder if it doesn't exist
os.makedirs(output_folder_path, exist_ok=True)

# Iterate through each subfolder (001 to 120)
for i in range(1, 93):
    subfolder_name = f"{i:03}"  # Format to match "001", "002", etc.
    subfolder_path = os.path.join(main_folder_path, subfolder_name, "RecordedVideo")
    
    # Check if "Recorded_Video" folder exists in the subfolder
    if not os.path.exists(subfolder_path):
        print(f"No 'Recorded_Video' folder found in {subfolder_path}")
        continue

    # Track the largest video file that meets the condition
    largest_video_file = None
    largest_video_size = 0  # Initialize with 0 to compare file sizes

    # Iterate through each video in the "Recorded_Video" folder
    for video_file in os.listdir(subfolder_path):
        if video_file.endswith((".mp4", ".avi", ".mov")):  # Adjust based on your video file formats
            # Check if the step number (3rd element separated by '_') is '23'
            file_parts = video_file.split("_")
            if len(file_parts) > 2 and file_parts[2] == "23":
                # Get the full file path
                video_path = os.path.join(subfolder_path, video_file)
                
                # Get the size of the current video file
                video_size = os.path.getsize(video_path)

                # Update if this file is larger than the previously found file
                if video_size > largest_video_size:
                    largest_video_size = video_size
                    largest_video_file = video_file

    # Process the largest video file if found
    if largest_video_file:
        video_path = os.path.join(subfolder_path, largest_video_file)
        cap = cv2.VideoCapture(video_path)

        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        frame_positions = [
            int(frame_count * 0.10),  # 10% of the video
            int(frame_count * 0.50),  # 50% of the video
            int(frame_count * 0.90),  # 90% of the video
        ]

        # Extract and save frames
        for idx, frame_position in enumerate(frame_positions, start=1):
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_position)
            success, frame = cap.read()
            if success:
                # Define output filename as original filename with a sequence number
                output_filename = f"{os.path.splitext(largest_video_file)[0]}_frame_{idx}.jpg"
                output_path = os.path.join(output_folder_path, output_filename)
                cv2.imwrite(output_path, frame)
                print(f"Saved frame {idx} of {largest_video_file} to {output_path}")
            else:
                print(f"Failed to read frame at position {frame_position} in {largest_video_file}")

        cap.release()

print("Frame extraction complete.")

#%% md
## Footnote
- Copyright：Ivan Liu 
- Last Update: 2024
- Env：course_9
- References: 
    - https://github.com/PrudhviGNV/Speech-Emotion-Recognization