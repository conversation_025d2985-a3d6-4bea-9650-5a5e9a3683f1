#%% md
# Pose Analysis
#%% md
## Feature Extraction
#%% md
### Environment Preparation
#%%
import cv2
import mediapipe as mp
import numpy as np
import matplotlib.pyplot as plt
import cv2 as cv
import numpy as np
import matplotlib.pyplot as plt
import time
import cv2
import imutils
from imutils.video import FileVideoStream
from IPython.display import display, HTML
import cv2
import mediapipe as mp
import numpy as np
import matplotlib.pyplot as plt
from concurrent.futures import ThreadPoolExecutor, as_completed
from joblib import Parallel, delayed
import numpy as np
import cv2
from concurrent.futures import ThreadPoolExecutor, as_completed
import cv2
import numpy as np
import os
import pandas as pd
import psychai
import psychai.feature.feature_extraction.feature_processor

# Get the parent directory
# This is useful if you need to import modules from a parent directory.
import sys
parent_dir = os.path.abspath('../')
sys.path.append(parent_dir)

# Import any custom utilities from the parent directory (if needed)
import utilities

path_pose_features_cache = r"../results/pose/pose_features_cache_20241101_2.pkl"
path_pose_features_csv = r"../results/pose/pose_features_cache_20241108.csv"

#%%
processor = psychai.feature.feature_extraction.feature_processor.FeatureProcessor()
#%% md
### Function Definitions
#%%
# class FeatureProcessor:

#     def __init__(self):
#         pass

#     def summarize_features(self, pose_features, strategies, all_feature_names):
#         """
#         Summarize the extracted features based on the provided strategies (e.g., mean, variance).

#         Args:
#         pose_features (np.array): Extracted pose features for each frame.
#         strategies (list): List of summarization strategies to apply.
#         all_feature_names (list): Original feature names.

#         Returns:
#         np.array: Combined summarized features.
#         list: Updated list of feature names with strategy prefixes.
#         """
#         all_features = []
#         combined_feature_names = []

#         # Apply each summarization strategy (e.g., mean, variance) to the features
#         for strategy in strategies:
#             if strategy == 'mean':
#                 summarized_features = self.summarize_mean(pose_features)
#             elif strategy == 'variance':
#                 summarized_features = self.summarize_variance(pose_features)
#             elif strategy == 'end_to_begin':
#                 summarized_features = self.summarize_end_to_begin(pose_features, len(pose_features))
#             elif strategy == 'max':
#                 summarized_features = self.summarize_max(pose_features)
#             else:
#                 raise ValueError(f"Invalid strategy '{strategy}'")

#             feature_names = self.add_strategy_prefix(strategy, all_feature_names)
#             all_features.append(summarized_features)
#             combined_feature_names.extend(feature_names)

#         return np.hstack(all_features), combined_feature_names
    
    
#     # Helper functions for summarization and feature names (unchanged from original code)

#     def summarize_mean(self, pose_features):
#         return np.mean(pose_features, axis=0) if pose_features.size else np.array([])

#     def summarize_variance(self, pose_features):
#         return np.var(pose_features, axis=0) if pose_features.size else np.array([])

#     def summarize_end_to_begin(self, pose_features, total_frames):
#         tenth_frames = max(10, total_frames // 10)
#         first_tenth = pose_features[:tenth_frames]
#         last_tenth = pose_features[-tenth_frames:]
#         first_mean = np.mean(first_tenth, axis=0)
#         last_mean = np.mean(last_tenth, axis=0)
#         return last_mean / (first_mean + 1e-6)

#     def summarize_max(self, pose_features):
#         return np.max(np.abs(pose_features), axis=0) if pose_features.size else np.array([])

#     def add_strategy_prefix(self, strategy, feature_names):
#         return [f"{strategy}_{name}" for name in feature_names]
#%%
# Initialize MediaPipe Pose and Drawing modules
mp_pose = mp.solutions.pose
mp_drawing = mp.solutions.drawing_utils



class PoseFeatureExtractor:
    """
    A class to process pose data from a video and extract pose features using MediaPipe Pose.
    
    Attributes:
    video_path (str): Path to the input video.
    frame_rate (int): Frame rate of the video for sampling.
    downsample_factor (int): How often to process a frame (e.g., process every nth frame).
    custom_features (list): List of custom features to extract.
    """
    
    def __init__(self, video_path, frame_rate=30, downsample_factor=1, custom_features=None):
        self.video_path = video_path  # Path to the video file
        self.frame_rate = frame_rate  # Frame rate to control video sampling
        self.downsample_factor = downsample_factor  # Frequency to downsample the video frames
        self.custom_features = custom_features  # Any custom features that need to be calculated

    def get_feature_names(self):
        return [f'landmark_{i}_{axis}' for i in range(33) for axis in ['x', 'y', 'z']]

    def process_frame(self, frame, frame_processor):
        """
        Process a single frame to extract pose landmarks using MediaPipe.

        Args:
        frame (np.array): A single frame from the video.
        frame_processor : object for processing.

        Returns:
        np.array: A list of flattened pose landmark coordinates (x, y, z) or None if no landmarks are found.
        """
        # Convert the frame from BGR to RGB for MediaPipe processing
        image_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        # results = frame_processor.process(image_rgb)  # Process the frame to get pose landmarks
        try:
            results = frame_processor.process(image_rgb)  # Process the frame to get pose landmarks
        except Exception as e:
            print(f"Error processing frame: {e}")
            results = None  # Handle the error by setting results to None or any fallback value
            
        # If pose landmarks are detected, extract them
        if results.pose_landmarks:
            frame_landmarks = [landmark for lm in results.pose_landmarks.landmark
                               for landmark in (lm.x, lm.y, lm.z)]  # Flatten landmarks into a list
            return np.array(frame_landmarks)
        return None  # Return None if no landmarks are found


    def add_custom_features(self, frame_landmarks, all_feature_names):
        """
        Add custom features to the frame landmarks if any custom features are defined.

        Args:
        frame_landmarks (np.array): The pose landmarks for a frame.
        all_feature_names (list): List of feature names.

        Returns:
        np.array: Updated landmarks with custom features (if any).
        """
        if self.custom_features:
            custom_values, custom_names = self.calculate_custom_features_row(frame_landmarks, self.custom_features)
            frame_landmarks = np.hstack([frame_landmarks, custom_values])
            # Add custom feature names to the full feature list
            if not hasattr(self, 'custom_names_extended'):
                all_feature_names.extend(custom_names)
                self.custom_names_extended = True
        return frame_landmarks



    def plot_features(self, pose_features, processed_frames, all_feature_names, features_to_plot):
        """
        Plot selected features over time.

        Args:
        pose_features (np.array): Extracted pose features over time.
        processed_frames (int): Number of processed frames.
        all_feature_names (list): List of all feature names.
        features_to_plot (list): List of features to plot (optional).
        """
        if features_to_plot is None or len(features_to_plot) == 0:
            features_to_plot = []
        elif features_to_plot == ['All']:
            features_to_plot = all_feature_names

        feature_indices = [all_feature_names.index(f) for f in features_to_plot if f in all_feature_names]

        if feature_indices:
            time_stamps = np.arange(0, processed_frames) * (self.downsample_factor / self.frame_rate)
            plt.figure(figsize=(12, 6))

            for idx in feature_indices:
                plt.plot(time_stamps, pose_features[:, idx], label=all_feature_names[idx])

            plt.xlabel('Time (s)')
            plt.ylabel('Feature Value')
            plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            plt.title('Pose Features Over Time')
            plt.tight_layout(rect=[0, 0, 0.85, 1])
            plt.show()

    def calculate_custom_features_row(self, pose_row, custom_features):
        custom_feature_values = []
        custom_feature_names = []
        for custom in custom_features:
            operation = custom[0]
            if operation in ['sum', 'difference']:
                feature_a, feature_b, new_name = custom[1], custom[2], custom[3]
                a_values = pose_row[feature_a * 3:feature_a * 3 + 3]
                b_values = pose_row[feature_b * 3:feature_b * 3 + 3]
                result_values = a_values + b_values if operation == 'sum' else a_values - b_values
                custom_feature_values.extend(result_values)
                custom_feature_names.extend([f"{new_name}_{axis}" for axis in ['x', 'y', 'z']])
            elif operation == 'angle':
                p0 = pose_row[custom[1] * 3: custom[1] * 3 + 3]
                p1 = pose_row[custom[2] * 3: custom[2] * 3 + 3]
                p2 = pose_row[custom[3] * 3: custom[3] * 3 + 3]
                v1, v2 = p0 - p1, p2 - p1
                angle = np.arccos(np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2)))
                custom_feature_values.append(angle)
                custom_feature_names.append(custom[4])
        return np.array(custom_feature_values), custom_feature_names



    # This one works well:

    def process_video(self, frame_processor):
        """
        Process the video frame by frame to extract pose features and apply summarization strategies.

        Args:
        frame_processor (callable): A callable that processes a frame and returns pose landmarks.
        use_parallel (bool): Whether to use parallel processing for faster computation.

        Returns:
        list: Names of the summarized features.
        np.array: Summarized pose features.
        int: Count of processed frames.
        """
        # Open the video file
        cap = cv2.VideoCapture(self.video_path)

        pose_features = []  # Store pose features of all processed frames
        all_feature_names = self.get_feature_names()  # Get default pose feature names
        processed_frames = 0  # Initialize processed frame counter

        frame_index = 0
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
            # Downsample frames: Only process every nth frame
            if frame_index % self.downsample_factor == 0:
                frame_landmarks = self.process_frame(frame, frame_processor)
                if frame_landmarks is not None:
                    # Add custom features if needed
                    frame_landmarks = self.add_custom_features(frame_landmarks, all_feature_names)
                    pose_features.append(frame_landmarks)
                    processed_frames += 1  # Increment only for successfully processed frames
            frame_index += 1
        cap.release()

        # Convert the pose features list to a numpy array
        pose_features = np.array(pose_features)

        return all_feature_names, pose_features, processed_frames


#%%
def summarized_process_folder(file):
    return extract_pose_features(file, False)

def get_frame_rate(file_path):
    # Open the video file
    video = cv2.VideoCapture(file_path)
    
    # Retrieve the frame rate
    frame_rate = video.get(cv2.CAP_PROP_FPS)
    
    # Release the video file
    video.release()
    
    # Return the frame rate
    return frame_rate

def extract_pose_features(file, show_graph):
    custom_features = [
        ['difference', 7, 8, 'ear_to_ear_difference'],
        ['difference', 11, 12, 'shoulder_to_shoulder_difference'],
        ['difference', 0, 9, 'nose_to_lip_difference'],
        ['angle', 0, 12, 11, 'left_head_angle'],
        ['angle', 12, 11, 0, 'right_head_angle']
    ]
    sampling_time = 30
    frame_rate = get_frame_rate(file)
    skip_frames = int(sampling_time * frame_rate)

    extractor = PoseFeatureExtractor(file, frame_rate, downsample_factor=skip_frames, custom_features=custom_features)

    # Extract features using multiple strategies and plot them
    strategies = ['mean', 'variance', 'max', 'end_to_begin']

    with mp_pose.Pose(static_image_mode=False, min_detection_confidence=0.5, min_tracking_confidence=0.5) as pose:
        feature_names, features, processed_frames = extractor.process_video(pose)

    # processor = FeatureProcessor()
    # Summarize features using the specified strategies
    processor = psychai.feature.feature_extraction.feature_processor.FeatureProcessor()

    # Summarize features using the specified strategies
    summarized_features, summarized_feature_names = processor.summarize_features(features, strategies, feature_names)

    if show_graph:
        # Plot selected features over time, if requested
        extractor.plot_features(features, processed_frames, feature_names, features_to_plot=
                                                        ['ear_to_ear_difference_x','ear_to_ear_difference_y',
                                                        'shoulder_to_shoulder_difference_x','shoulder_to_shoulder_difference_y',
                                                        'nose_to_lip_difference_x','nose_to_lip_difference_y',
                                                        'left_head_angle','right_head_angle'])

        print(processed_frames)
        print(len(feature_names))
        print(len(features))
        print(features[0])

    return summarized_features, summarized_feature_names
#%% md
### Mediapipe image example

这段代码通过MediaPipe的Pose模型来检测图像中人体的姿态，包括关键点的定位和身体部位之间的连接。代码首先配置了Pose模型，包括开启静态图像模式、设置模型复杂度、启用背景分割以及设置最小检测置信度。接着，读取一张图像，将其从BGR格式转换为RGB格式，并通过Pose模型处理。处理结果包括身体姿态的关键点定位和背景分割掩码。代码接着创建了一个注释图像，其中背景被设置为灰色，并在图像上绘制了身体的姿态关键点和连接线。最后，显示了注释图像，并绘制了Pose世界坐标系中的关键点。
#%%
image_path_1 = r"D:\Programming3\psychai\example\paper5_mmer_moral\resources\images\pose_example_1.jpg"
image_path_2 = r"D:\Programming3\psychai\example\paper5_mmer_moral\resources\images\pose_example_2.jpg"


def mediapipe_draw(image):
    # 初始化绘图工具和绘图风格
    mp_drawing = mp.solutions.drawing_utils
    mp_drawing_styles = mp.solutions.drawing_styles
    # 初始化Pose检测模型
    mp_pose = mp.solutions.pose

    # 设置背景颜色为灰色
    BG_COLOR = (192, 192, 192)  # 灰色

    # 配置Pose模型参数
    pose = mp_pose.Pose(
        static_image_mode=True,  # 静态图像模式
        model_complexity=2,  # 模型复杂度
        enable_segmentation=True,  # 启用背景分割
        min_detection_confidence=0.5)  # 最小检测置信度


    image_height, image_width, _ = image.shape

    # 在处理前将BGR图像转换为RGB
    converted_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    results = pose.process(converted_image)

    # 复制原图像用于注释
    annotated_image = converted_image.copy()

    # 在图像上绘制分割结果
    # 如果需要在边界周围改进分割效果，可以考虑对"results.segmentation_mask"和"image"应用联合双边滤波
    condition = np.stack((results.segmentation_mask,) * 3, axis=-1) > 0.1
    bg_image = np.zeros(image.shape, dtype=np.uint8)
    bg_image[:] = BG_COLOR  # 设置背景图像为灰色
    annotated_image = np.where(condition, annotated_image, bg_image)  # 应用条件，合成最终的注释图像

    # 定义更粗的线条和关键点样式
    thicker_line_spec = mp_drawing.DrawingSpec(thickness=6, circle_radius=3, color=(0,0,255))
    thicker_connection_spec = mp_drawing.DrawingSpec(thickness=4, color=(0,255,0))


    # 在图像上绘制Pose特征点和连接线
    mp_drawing.draw_landmarks(
        annotated_image,
        results.pose_landmarks,
        mp_pose.POSE_CONNECTIONS,
        landmark_drawing_spec=thicker_line_spec,  # 使用自定义的关键点样式
        connection_drawing_spec=thicker_connection_spec  # 使用自定义的连接线样式
    )

    # 显示带有Pose特征点的图像
    plt.imshow(annotated_image)
    plt.show()

    # 绘制Pose的世界坐标系中的特征点
    mp_drawing.plot_landmarks(
        results.pose_world_landmarks, mp_pose.POSE_CONNECTIONS)

#%%
# 读取图像
image_1 = cv2.imread(image_path_1)
mediapipe_draw(image_1)
#%%
# 读取图像
image_2 = cv2.imread(image_path_2)
mediapipe_draw(image_2)
#%% md
### Openpose single image example

OpenPose is an open-source library for real-time multi-person 2D pose estimation, capable of detecting key points for the body, face, hands, and feet. Originally developed by the Perceptual Computing Lab at Carnegie Mellon University, it has become a benchmark project in the field of pose estimation.

Key Features
- Real-Time Capability: OpenPose can detect poses in real-time video streams, making it suitable for applications requiring immediate feedback, such as interactive art, sports analysis, and health monitoring.
- Multi-Person Detection: Unlike some pose estimation systems that can only handle a single person, OpenPose can detect poses for multiple people simultaneously in complex scenes.
- Multi-Part Detection: It can detect and track key points across multiple body parts, including the body, face, hands, and feet.
- Cross-Platform: OpenPose supports Windows, Linux, and Mac operating systems and can run on various hardware setups, including NVIDIA GPUs and CPUs.
- Open Source: As an open-source project, OpenPose encourages community contributions and application development, which continuously enhances its capabilities and broadens its applicability.

Here, we use features extracted with OpenPose online and a DNN model (graph_opt.pb) trained on the MobileNet dataset.

#%%
BODY_PARTS = { "Nose": 0, "Neck": 1, "RShoulder": 2, "RElbow": 3, "RWrist": 4,
               "LShoulder": 5, "LElbow": 6, "LWrist": 7, "RHip": 8, "RKnee": 9,
               "RAnkle": 10, "LHip": 11, "LKnee": 12, "LAnkle": 13, "REye": 14,
               "LEye": 15, "REar": 16, "LEar": 17, "Background": 18 }

POSE_PAIRS = [ ["Neck", "RShoulder"], ["Neck", "LShoulder"], ["RShoulder", "RElbow"],
               ["RElbow", "RWrist"], ["LShoulder", "LElbow"], ["LElbow", "LWrist"],
               ["Neck", "RHip"], ["RHip", "RKnee"], ["RKnee", "RAnkle"], ["Neck", "LHip"],
               ["LHip", "LKnee"], ["LKnee", "LAnkle"], ["Neck", "Nose"], ["Nose", "REye"],
               ["REye", "REar"], ["Nose", "LEye"], ["LEye", "LEar"] ]

image_width=368
image_height=368
# 门槛值
threshold=0.2
# 將圖片讀入
img_1 = cv.imread(image_path_1,cv.IMREAD_UNCHANGED)
img_2 = cv.imread(image_path_2,cv.IMREAD_UNCHANGED)
#%%
def appen_detection_result_2(raw_image, image_file):

    # 載入預訓練好的模型
    net = cv.dnn.readNetFromTensorflow("D:/Programming3/psychai/tutorial_simplified_chinese/feature_extraction/models/openpose/graph_opt.pb")
    
    photo_height=image_file.shape[0]
    photo_width=image_file.shape[1]
    net.setInput(cv.dnn.blobFromImage(image_file, 1.0, (image_width, image_height), (127.5, 127.5, 127.5), swapRB=True, crop=False))

    out = net.forward()
    out = out[:, :19, :, :] 

    assert(len(BODY_PARTS) == out.shape[1])

    points = []
    for i in range(len(BODY_PARTS)):
            # Slice heatmap of corresponging body's part.
        heatMap = out[0, i, :, :]

            # Originally, we try to find all the local maximums. To simplify a sample
            # we just find a global one. However only a single pose at the same time
            # could be detected this way.
        _, conf, _, point = cv.minMaxLoc(heatMap)
        x = (photo_width * point[0]) / out.shape[3]
        y = (photo_height * point[1]) / out.shape[2]
        # Add a point if it's confidence is higher than threshold.
        points.append((int(x), int(y)) if conf > threshold else None)


    for pair in POSE_PAIRS:
        partFrom = pair[0]
        partTo = pair[1]
        assert(partFrom in BODY_PARTS)
        assert(partTo in BODY_PARTS)

        idFrom = BODY_PARTS[partFrom]
        idTo = BODY_PARTS[partTo]

        if points[idFrom] and points[idTo]:
            cv.line(raw_image, points[idFrom], points[idTo], (0, 255, 0), 3)
            cv.ellipse(raw_image, points[idFrom], (6, 6), 0, 0, 360, (0, 0, 255), cv.FILLED)
            cv.ellipse(raw_image, points[idTo], (6, 6), 0, 0, 360, (0, 0, 255), cv.FILLED)

    t, _ = net.getPerfProfile()

    face_img = raw_image.copy() #建立复本,避免影响原本的图像
    face_img_RGB = cv.cvtColor(face_img, cv.COLOR_BGR2RGB) # OpenCV用BGR顺序,而matplotlib用RGB, 所以必须做一次转换 
    plt.imshow(face_img_RGB)
#%%
appen_detection_result_2(img_1, img_1)
#%%
appen_detection_result_2(img_2, img_2)
#%% md
### Video Example
#%%
image_path = '../resources/images/mediapipe_pose_landmarker.png'
display(HTML(f'<img src="{image_path}" style="height: 400px;" />'))
#%% md
### Data Preparation
#%%
single_analysis = utilities.SingleModalityAnalysis()

# base_directory = r"D:\Programming3\psychai\example\paper5_mmer_moral\resources\raw_data"
# base_directory = r"K:\Backup\Experiments\Moral Elevation\Disk1_2_combined\FS-9 Day 1"
base_directory = r"F:\Experiments\Moral Elevation\Disk1_2_combined\FS-9 Day 1"

filters = [(2, '23')]
user_ids = range(1,120)
merging_pdf = pd.read_csv('../resources/data/rct/rct.csv')

df_largest = single_analysis.prosss_folder(base_directory, filters, user_ids, merging_pdf, modality_value="RecordedVideo")
#%%
import psychai.feature.feature_extraction.feature_retriever as retriever

processor = retriever.FeatureRetriever(df_largest, summarized_feature_extractors=[summarized_process_folder]
                             ,cache_file_path=path_pose_features_cache)

# Extract features and return the final DataFrame
df_with_features = processor.extract_features()
df_with_features.to_csv(path_pose_features_csv)
#%%
df_with_features
#%% md
## Footnote
- Copyright：Ivan Liu 
- Last Update: 2024
- Env：course_６ for feature extraction, psychai241104 for emotion recognition
- References:
    - https://github.com/CMU-Perceptual-Computing-Lab/openpose
    - https://github.com/quanhua92/human-pose-estimation-opencv
    - https://github.com/opencv/opencv/blob/master/samples/dnn/openpose.py