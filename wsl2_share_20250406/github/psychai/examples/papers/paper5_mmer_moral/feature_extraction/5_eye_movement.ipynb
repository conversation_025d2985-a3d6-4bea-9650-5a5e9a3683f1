#%% md
# Eye-Movement Emotion Recognition
#%% md
## Feature Extraction
#%% md
### Function Definition
#%%
import cv2
import mediapipe as mp
import pandas as pd
import numpy as np
import psychai.feature.feature_extraction.feature_retriever as feature_retriever
import psychai.feature.feature_extraction.feature_processor as feature_processor
import os
import pickle

class EyeFeatureExtractor:
    def __init__(self, output_video_folder_path= r"C:\Working\Programming\Python\psychai_v1\example\paper5_mmer_moral\results\eye_movement\videos",
                 result_cache_folder_path = r"C:\Working\Programming\Python\psychai_v1\example\paper5_mmer_moral\results\eye_movement\cache", 
                 generate_output_video = False,):
        self.output_video_folder_path = output_video_folder_path
        self.generate_output_video = generate_output_video
        self.result_cache_folder_path = result_cache_folder_path
        pass

    def process_video(self, input_video_path):

        # Initialize MediaPipe FaceMesh
        mp_face_mesh = mp.solutions.face_mesh
        mp_drawing = mp.solutions.drawing_utils
        base_name = os.path.basename(input_video_path)
        out_put_file_path = os.path.join(self.output_video_folder_path,base_name)
        #cache_file_path = os.path.join(self.result_cache_folder_path,base_name)
        cache_file_path = os.path.join(self.result_cache_folder_path, f"{os.path.splitext(base_name)[0]}.pkl")

        if os.path.exists(cache_file_path):
            # Load df_results from the pickle file
            with open(cache_file_path, 'rb') as file:
                df_results = pickle.load(file)
            print(f"[File {input_video_path}] DataFrame loaded from pickle file.")
            return np.array(df_results), df_results.columns.tolist()

        # Define landmark indices for the iris and eye corners and top/bottom of eye
        LEFT_IRIS_CENTER = 468
        RIGHT_IRIS_CENTER = 473
        LEFT_EYE_OUTER_CORNER = 33
        LEFT_EYE_INNER_CORNER = 133
        RIGHT_EYE_OUTER_CORNER = 263
        RIGHT_EYE_INNER_CORNER = 362
        LEFT_EYE_TOP = 159
        LEFT_EYE_BOTTOM = 145
        RIGHT_EYE_TOP = 386
        RIGHT_EYE_BOTTOM = 374


        # Open the input video
        cap = cv2.VideoCapture(input_video_path)
        if not cap.isOpened():
            print(f"Error: Unable to open video file {input_video_path}")
            exit()

        # Get video properties for setting up the VideoWriter
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        if self.generate_output_video:
            out = cv2.VideoWriter(out_put_file_path, fourcc, fps, (width, height))

        # Initialize DataFrame to store results
        results = []

        # Initialize the FaceMesh model
        with mp_face_mesh.FaceMesh(
                static_image_mode=False,
                max_num_faces=1,
                refine_landmarks=True,  # This enables iris landmarks
                min_detection_confidence=0.5,
                min_tracking_confidence=0.5) as face_mesh:

            frame_number = 0
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break

                # Convert the frame color to RGB
                image_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                # Process the frame to detect face landmarks
                results_landmarks = face_mesh.process(image_rgb)

                if results_landmarks.multi_face_landmarks:
                    for face_landmarks in results_landmarks.multi_face_landmarks:
                        # Get the eye corners, iris centers, and top/bottom eye landmarks
                        left_eye_outer = face_landmarks.landmark[LEFT_EYE_OUTER_CORNER]
                        left_eye_inner = face_landmarks.landmark[LEFT_EYE_INNER_CORNER]
                        left_eye_top = face_landmarks.landmark[LEFT_EYE_TOP]
                        left_eye_bottom = face_landmarks.landmark[LEFT_EYE_BOTTOM]
                        right_eye_outer = face_landmarks.landmark[RIGHT_EYE_OUTER_CORNER]
                        right_eye_inner = face_landmarks.landmark[RIGHT_EYE_INNER_CORNER]
                        right_eye_top = face_landmarks.landmark[RIGHT_EYE_TOP]
                        right_eye_bottom = face_landmarks.landmark[RIGHT_EYE_BOTTOM]
                        left_iris_center = face_landmarks.landmark[LEFT_IRIS_CENTER]
                        right_iris_center = face_landmarks.landmark[RIGHT_IRIS_CENTER]

                        # Calculate eye region width and height for left eye
                        left_eye_width = abs(left_eye_outer.x - left_eye_inner.x)
                        left_eye_height = abs(left_eye_top.y - left_eye_bottom.y)

                        # Calculate relative position of left iris to the eye frame
                        mean_left_y = (left_eye_outer.y + left_eye_inner.y) / 2
                        left_iris_relative_x = (left_iris_center.x - left_eye_outer.x) / left_eye_width
                        left_iris_relative_y = (mean_left_y - left_iris_center.y) / left_eye_width

                        # Calculate eye region width and height for right eye
                        right_eye_width = abs(right_eye_outer.x - right_eye_inner.x)
                        right_eye_height = abs(right_eye_top.y - right_eye_bottom.y)

                        # Calculate relative position of right iris to the eye frame
                        mean_right_y = (right_eye_outer.y + right_eye_inner.y) / 2
                        right_iris_relative_x = (right_iris_center.x - right_eye_inner.x) / right_eye_width
                        right_iris_relative_y = (mean_right_y - right_iris_center.y) / right_eye_width

                        # Append data to results list
                        results.append({
                            'file_path': input_video_path,
                            'frame_number': frame_number,
                            'left_iris_relative_x': left_iris_relative_x,
                            'left_iris_relative_y': left_iris_relative_y,
                            'right_iris_relative_x': right_iris_relative_x,
                            'right_iris_relative_y': right_iris_relative_y,
                            'left_eye_width': left_eye_width,
                            'left_eye_height': left_eye_height,
                            'right_eye_width': right_eye_width,
                            'right_eye_height': right_eye_height
                        })

                        if self.generate_output_video:
                            # Overlay the values on the video frame
                            cv2.putText(frame, f'Frame: {frame_number}', (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                            cv2.putText(frame, f'Left Iris X: {left_iris_relative_x:.3f}', (10, 40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                            cv2.putText(frame, f'Left Iris Y: {left_iris_relative_y:.3f}', (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                            cv2.putText(frame, f'Right Iris X: {right_iris_relative_x:.3f}', (10, 80), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                            cv2.putText(frame, f'Right Iris Y: {right_iris_relative_y:.3f}', (10, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                            cv2.putText(frame, f'Left Eye W: {left_eye_width:.3f}', (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                            cv2.putText(frame, f'Left Eye H: {left_eye_height:.3f}', (10, 140), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                            cv2.putText(frame, f'Right Eye W: {right_eye_width:.3f}', (10, 160), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                            cv2.putText(frame, f'Right Eye H: {right_eye_height:.3f}', (10, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

                            # Draw the face mesh with iris and eye points
                            mp_drawing.draw_landmarks(
                                image=frame,
                                landmark_list=face_landmarks,
                                connections=mp_face_mesh.FACEMESH_TESSELATION,
                                landmark_drawing_spec=None,
                                connection_drawing_spec=mp_drawing.DrawingSpec(color=(216, 216, 216), thickness=1, circle_radius=1)
                            )

                # Write the processed frame to the output video
                if self.generate_output_video:
                    out.write(frame)
                frame_number += 1

            # Convert results to DataFrame
            df_results = pd.DataFrame(results)

            # Release resources
            cap.release()
            if self.generate_output_video:
                out.release()
            cv2.destroyAllWindows()
            
        with open(cache_file_path, 'wb') as file:
            pickle.dump(df_results, file)
            print(f"[File {input_video_path}] DataFrame generated and saved to pickle file.")

        # Return the DataFrame with results
        return np.array(df_results), df_results.columns.tolist()

    def summarized_process_folder(self,input_video_path):
         
        # Extract features using multiple strategies and plot them
        strategies = ['mean', 'variance', 'max', 'end_to_begin']
        
        try:
            features, feature_names = self.process_video(input_video_path)
        except Exception as e:
            print(f"Error: {e}")
            return None, None   

        # Summarize features using the specified strategies
        processor = feature_processor.FeatureProcessor()
        summarized_features, summarized_feature_names = processor.summarize_features(features, strategies, feature_names)

        return summarized_features, summarized_feature_names   
#%% md
### Single File
#%%
input_video_path = r"C:\Working\Programming\Python\psychai_v1\example\resources\videos\Ivan_Eye.mp4"
output_video_path = r"C:\Working\Programming\Python\psychai_v1\example\paper5_mmer_moral\results\eye_movement\videos\PS-9_005_6_05_25_12_26_03.mp4"
extractor = EyeFeatureExtractor()
features, column_names = extractor.summarized_process_folder(input_video_path,output_video_path )


#%%
df_result = pd.DataFrame([features], columns=column_names)
df_result = df_result.loc[:, ~df_result.columns.str.contains('frame')]
df_result
#%% md
### Folder Example
#%%
import sys
import os
import pandas as pd

parent_dir = os.path.abspath('../')
sys.path.append(parent_dir)

# Import any custom utilities from the parent directory (if needed)
import utilities

base_directory = r"W:"

# Create a FileFilter instance with multiple attribute filters
filters = [(2, '25'),(2, '26'),(2, '27'),(2, '28')]
file_filter = utilities.FileFilter(base_directory, attribute_filters=filters, modality_value='RecordedVideo')

# Get matching files for user IDs from 1 to 13
df = file_filter.get_matching_files(range(1, 120))

# Load the rct.csv file
rct_df = pd.read_csv('../resources/data/rct/rct.csv')

# Merge the two dataframes based on the 'user_id' column
df_updated = pd.merge(df, rct_df[['user_id', 'Group']], on='user_id', how='left')

# Find the row with the largest file_size for each user_id
#df_largest = df_updated.loc[df_updated.groupby('user_id','attribute_value')['file_size'].idxmax()]
# Group by 'user_id' and 'attribute_value', then select the rows with the largest 'file_size'
df_largest = df_updated.loc[df_updated.groupby(['user_id', 'attribute_value'])['file_size'].idxmax()]


# Reset the index if needed
df_largest.reset_index(drop=True, inplace=True)

# Display the result
print(df_largest)
#%%
# import sys
# import os

# parent_dir = os.path.abspath('../')
# sys.path.append(parent_dir)

# # Import any custom utilities from the parent directory (if needed)
# import utilities

# single_analysis = utilities.SingleModalityAnalysis()

# #base_directory = r"C:\Working\Programming\Python\psychai_v1\example\paper5_mmer_moral\resources\raw_data"
# base_directory = r"W:"

# filters = [(2, '25'), (2, '26'), (2, '27'), (2, '28')]
# user_ids = range(1, 120)
# merging_pdf = pd.read_csv('../resources/data/rct/rct.csv')

# df_largest = single_analysis.prosss_folder(base_directory, filters, user_ids, merging_pdf, modality_value="RecordedVideo")
#%%
extractor = EyeFeatureExtractor()
processor = feature_retriever.FeatureRetriever(df_largest, summarized_feature_extractors=[extractor.summarized_process_folder]
                             ,cache_file_path="../results/eye_movement/cache/eye_movement_features_cache_20241106.pkl")

df_with_features = processor.extract_features()
df_with_features.to_csv("../results/eye_movement/csv/eye_movement_features_cache_20241106.csv")
#%% md
## Footnote
- Copyright：Ivan Liu 
- Last Update: 2024
- Env：psychai241104
- References: 