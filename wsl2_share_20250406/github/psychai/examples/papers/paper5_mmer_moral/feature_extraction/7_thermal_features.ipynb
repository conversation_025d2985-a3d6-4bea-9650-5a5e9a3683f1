#%% md
# Facial Thermal Feature Extraction
#%% md
## Feature Extraction


#%% md
### Environment Preparation
#%%
import os
import sys
import concurrent.futures
import time
import datetime
import subprocess
import datetime
import concurrent.futures
import json
import cv2
import numpy as np
from concurrent.futures import Process<PERSON>oolExecutor, as_completed
from tqdm.notebook import tqdm
import pandas as pd

# Get the parent directory
# This is useful if you need to import modules from a parent directory.
parent_dir = os.path.abspath('..')
sys.path.append(parent_dir)

# Import any custom utilities from the parent directory (if needed)
import utilities

data_preprocessing = utilities.DataPreProcessing()
# Log file to track processed folders and steps
PROCESSED_LOG = "../results/facial/process_log_disk_6.json"
# PROCESSED_LOG = "../results/thermal/process_log_local.json"

main_folder = r'K:\Backup\Experiments\Moral Elevation\Disk1_2_combined\FS-9 Day 1'
# main_folder = r'D:\Programming3\psychai\example\paper5_mmer_moral\resources\raw_data'

#%% md
### File Preparation

The following code use files in folder "recorded_video" to segment videos from "thermal".
#%%

#%%
import numpy as np
import os
import pandas as pd

"""
This module contains classes and methods for transforming a BVP signal in a BPM signal.
"""

class Path:
    """
    Manage (multi-channel, row-wise) BVP signals, and transforms them in BPMs.
    """
    def __init__(self):
        pass

    def check_path_or_create(self, path):
        # Check if the path exists
        if not os.path.exists(path):
            # If it does not exist, create the directory
            os.makedirs(path)
        return path
    
# Step 1: Parse the "Time" folder file names to extract the time information
def parse_time_file(file_name):
    parts = file_name.split('_')
    user_number = parts[1]
    step = int(parts[2])
    month = int(parts[3])
    day = int(parts[4])
    end_hour = int(parts[5])
    end_minute = int(parts[6])
    end_second = int(parts[7].replace('.mp4', ''))

    return {
        'file_name': file_name,
        'user_number': user_number,
        'step': step,
        'month': month,
        'day': day,
        'hour': end_hour,
        'minute': end_minute,
        'second': end_second,
        'start_time': datetime.datetime(2022, month, day, end_hour, end_minute, end_second)
    }

def parse_thermal_file(file_path):
    try:
        # Extract the file name and split it into parts
        file_name = os.path.basename(file_path)
        file_name = file_name.replace('T', '_')
        parts = file_name.split('_')
        
        # Validate if the expected parts are in the file name
        if len(parts) < 3:
            raise ValueError(f"Filename format is incorrect: {file_name}")

        date = parts[1]
        time = parts[2].replace('.mp4', '')

        # Validate the date and time format
        if len(date) != 8 or len(time) != 6:
            raise ValueError(f"Date or time format in filename is incorrect: {file_name}")

        # Extract and convert date and time components from the filename
        year = int(date[:4])
        month = int(date[4:6])
        day = int(date[6:8])
        hour = int(time[:2])  # 12-hour format from filename
        minute = int(time[2:4])
        second = int(time[4:6])

        # Get the modified time of the file to determine AM or PM
        modified_time = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
        
        # Adjust hour based on modified time's 24-hour format as a reference
        if modified_time.hour >= 12 and hour < 12:
            hour += 12  # Convert PM hour to 24-hour format
        elif modified_time.hour < 12 and hour == 12:
            hour = 0  # Handle 12 AM case

        # Return parsed start time if successful
        return {
            'file_name': file_name,
            'start_time': datetime.datetime(year, month, day, hour, minute, second)
        }

    except (IndexError, ValueError) as e:
        print(f"Error parsing file: {file_name}. Details: {e}")
        return None

# Load and update processed log
def load_and_update_processed_log():
    # Load log data if it exists; otherwise, initialize an empty dictionary
    if os.path.exists(PROCESSED_LOG):
        with open(PROCESSED_LOG, 'r') as file:
            log_data = json.load(file)
    else:
        log_data = {}

    # Nested function to save the log
    def save_log():
        with open(PROCESSED_LOG, 'w') as file:
            json.dump(log_data, file, indent=4)

    return log_data, save_log
    
def segment_video_ffmpeg(input_video_path, start_time_seconds, end_time_seconds, output_video_path):
    command = [
        'ffmpeg', '-y',
        '-ss', str(start_time_seconds),
        '-i', input_video_path,
        '-t', str(end_time_seconds - start_time_seconds),
        '-c', 'copy',
        output_video_path
    ]
    result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    if result.returncode != 0:
        print("Error in FFmpeg command:", result.stderr.decode())
    else:
        print("Video segment created successfully:", output_video_path)

def rotate_if_horizontal_ffmpeg(thermal_file_path, input_video_path, output_video_path):
    # Open the video and get dimensions
    video_capture = cv2.VideoCapture(thermal_file_path)
    width = int(video_capture.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(video_capture.get(cv2.CAP_PROP_FRAME_HEIGHT))

    # Read the first frame for brightness analysis
    success, frame = video_capture.read()
    video_capture.release()

    if success:
        # Check if the video is horizontal
        is_horizontal = width > height

        if is_horizontal:
            # For horizontal videos, take upper and lower 10% strips
            upper_strip = frame[:height // 10, :]
            lower_strip = frame[-height // 10:, :]

            # Split each strip into left and right halves
            upper_left_half = upper_strip[:, :width // 4]
            upper_right_half = upper_strip[:, width // 4:]
            lower_left_half = lower_strip[:, :width // 4]
            lower_right_half = lower_strip[:, width // 4:]

            # Calculate the average brightness of each half for both strips
            upper_left_brightness = np.mean(cv2.cvtColor(upper_left_half, cv2.COLOR_BGR2GRAY))
            upper_right_brightness = np.mean(cv2.cvtColor(upper_right_half, cv2.COLOR_BGR2GRAY))
            lower_left_brightness = np.mean(cv2.cvtColor(lower_left_half, cv2.COLOR_BGR2GRAY))
            lower_right_brightness = np.mean(cv2.cvtColor(lower_right_half, cv2.COLOR_BGR2GRAY))

            # Average brightness for left and right sides across both strips
            left_brightness = (upper_left_brightness + lower_left_brightness) / 2
            right_brightness = (upper_right_brightness + lower_right_brightness) / 2

            # # Determine rotation direction based on brightness
            if right_brightness > left_brightness:
                # Rotate 90 degrees clockwise
                print(f"Rotating {input_video_path} 90 degrees clockwise...")
                #The error message indicates that FFmpeg encountered an issue with buffering the audio stream. The error "Too many packets buffered for output stream 0:1" often arises when FFmpeg tries to handle both video and audio streams, especially with formats that have low frame rates or non-standard encoding parameters.
                #Therefore Remove the Audio Stream
                command = [
                    'ffmpeg', '-y', '-i', input_video_path, '-vf', 'transpose=1',
                    '-c:v', 'libx264', '-an', output_video_path
                ]

            else:
                # Rotate 90 degrees counterclockwise
                print(f"Rotating {input_video_path} 90 degrees counterclockwise...")
                command = [
                    'ffmpeg', '-y', '-i', input_video_path, '-vf', 'transpose=2',
                    '-c:v', 'libx264', '-an', output_video_path
                ]

            # Run FFmpeg command and capture output
            result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Print debug information and check for errors
            if result.returncode != 0:
                print("FFmpeg error:", result.stderr.decode())
            else:
                print("Rotation successful. Output saved to:", output_video_path)

            # Print FFmpeg output for additional debugging (optional)
            print("FFmpeg output:", result.stdout.decode())
        else:
            # For vertical videos, take left and right 10% strips
            left_strip = frame[:, :width // 10]
            right_strip = frame[:, -width // 10:]

            # Split each strip into upper and lower halves
            left_upper_half = left_strip[:height // 2, :]
            left_lower_half = left_strip[height // 2:, :]
            right_upper_half = right_strip[:height // 2, :]
            right_lower_half = right_strip[height // 2:, :]

            # Calculate the average brightness of each half for both strips
            left_upper_brightness = np.mean(cv2.cvtColor(left_upper_half, cv2.COLOR_BGR2GRAY))
            left_lower_brightness = np.mean(cv2.cvtColor(left_lower_half, cv2.COLOR_BGR2GRAY))
            right_upper_brightness = np.mean(cv2.cvtColor(right_upper_half, cv2.COLOR_BGR2GRAY))
            right_lower_brightness = np.mean(cv2.cvtColor(right_lower_half, cv2.COLOR_BGR2GRAY))

            # Average brightness of upper and lower halves from both strips
            upper_brightness = (left_upper_brightness + right_upper_brightness) / 2
            lower_brightness = (left_lower_brightness + right_lower_brightness) / 2

            # Rotate 180 degrees if the upper half is lighter than the lower half
            if upper_brightness > lower_brightness:
                print(f"Rotating {input_video_path} 180 degrees as upper half is lighter...")
                command = [
                    'ffmpeg', '-y', '-i', input_video_path, '-vf', 'vflip,hflip',
                    '-c:v', 'libx264', '-an', output_video_path
                ]
                subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            else:
                print(f"Keeping {input_video_path} intact as upper half is darker.")
                command = [
                    'ffmpeg', '-y', '-i', input_video_path, '-c:v', 'copy',
                    '-c:v', 'libx264', '-an', output_video_path
                ]
                subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            # Adding basic error handling
            result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            if result.returncode != 0:
                print("FFmpeg error:", result.stderr.decode())
            else:
                print("Operation completed successfully. Output saved to:", output_video_path)

# Function to concatenate two videos using ffmpeg
def concatenate_videos(video1_path, video2_path, output_path):
    with open('concat_list.txt', 'w') as f:
        f.write(f"file '{video1_path}'\n")
        f.write(f"file '{video2_path}'\n")
    
    command = [
        'ffmpeg', '-y', '-f', 'concat', '-safe', '0',
        '-i', 'concat_list.txt', '-c', 'copy', output_path
    ]
    subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    os.remove('concat_list.txt')

# Function to process each step
def process_step(step, thermal_info, thermal_file_path, fps, duration, segmented_folder, user_number):
    step_id = str(step['step'])
    output_filename = f"PS-9_{user_number}_{step['step']}_{step['month']:02d}_{step['day']:02d}_{step['hour']:02d}_{step['minute']:02d}_{step['second']:02d}.mp4"
    output_path = os.path.join(segmented_folder, output_filename)

    video_start_time = thermal_info['start_time']
    segment_start_seconds = max((step['start_time'] - video_start_time).total_seconds(), 0)
    segment_end_seconds = min((step['end_time'] - video_start_time).total_seconds(), duration)

    if segment_start_seconds >= duration or segment_end_seconds <= 0:
        #print(f"Step {step['step']} does not overlap with the video time.")
        return None

    if segment_end_seconds - segment_start_seconds < 1:
        #print(f"The file {output_filename} is too short to save.")
        return None

    # Temporary file before rotation or concatenation
    temp_output_path = os.path.join(segmented_folder, "temp_" + output_filename)

    # Segment the video
    segment_video_ffmpeg(thermal_file_path, segment_start_seconds, segment_end_seconds, temp_output_path)

    # Temp file for the rotated video
    temp_rotated_output_path = os.path.join(segmented_folder, "rotated_" + output_filename)

    # Rotate the video if needed
    rotate_if_horizontal_ffmpeg(thermal_file_path, temp_output_path, temp_rotated_output_path)

    # Append the video if the output file already exists
    # if os.path.exists(output_path):
    #     temp_concat_output = os.path.join(segmented_folder, "concat_" + output_filename)
    #     if os.path.exists(temp_rotated_output_path):
    #         concatenate_videos(output_path, temp_rotated_output_path, temp_concat_output)
    #         os.replace(temp_concat_output, output_path)
    if os.path.exists(output_path):
        # Open the video file to get fps and duration
        video_capture = cv2.VideoCapture(output_path)
        fps = video_capture.get(cv2.CAP_PROP_FPS)
        frame_count = int(video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # If fps is zero, release and return as it would cause division error
        if fps == 0:
            video_capture.release()
            return
        
        # Calculate the duration
        duration = frame_count / fps
        video_capture.release()

        # Define the concatenated output path
        temp_concat_output = os.path.join(segmented_folder, "concat_" + output_filename)

        if duration > 1:
            # If duration > 1 second and temp_rotated_output_path exists, concatenate the videos
            if os.path.exists(temp_rotated_output_path):
                concatenate_videos(output_path, temp_rotated_output_path, temp_concat_output)
                if os.path.exists(temp_concat_output):
                    os.replace(temp_concat_output, output_path)
        else:
            # If duration <= 1 second, replace output_path with temp_rotated_output_path
            if os.path.exists(temp_rotated_output_path):
                os.replace(temp_rotated_output_path, output_path)
    else:
        if os.path.exists(temp_rotated_output_path):
            os.replace(temp_rotated_output_path, output_path)
        elif os.path.exists(temp_output_path):
            os.replace(temp_output_path, output_path)

    # Clean up temporary files
    if os.path.exists(temp_output_path):
        os.remove(temp_output_path)
    if os.path.exists(temp_rotated_output_path):
        os.remove(temp_rotated_output_path)

    print(f"Processed and saved: {output_filename}")
    return step_id

# Main function to segment the video based on steps and thermal info
def segment_video(thermal_file_path, steps, segmented_folder, user_number, processed_log, step_numbers=None):
    thermal_info = parse_thermal_file(thermal_file_path)

    # Filter steps based on the provided list of step_numbers
    if step_numbers is not None:
        steps = [step for step in steps if step['step'] in step_numbers]
        if not steps:
            #print(f"No matching steps found for step numbers: {step_numbers}")
            return

    # Open the video file to get fps and duration
    video_capture = cv2.VideoCapture(thermal_file_path)
    fps = video_capture.get(cv2.CAP_PROP_FPS)
    frame_count = int(video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
    if fps == 0:
        video_capture.release()
        return
    duration = frame_count / fps
    thermal_info['end_time'] = thermal_info['start_time'] + datetime.timedelta(seconds=duration)

    # Collect processed steps
    processed_steps = []

    for step in steps:
        result = process_step(step, thermal_info, thermal_file_path, fps, duration, segmented_folder, user_number)
        if result:
            processed_steps.append(result)


    # Update log with processed steps
    folder_key = user_number
    if folder_key not in processed_log:
        processed_log[folder_key] = {}
    for step_id in processed_steps:
        processed_log[folder_key][step_id] = True

    video_capture.release()


# Process each user folder
def process_user_folder(user_folder, step_numbers=None):
    # Load and prepare processed log and save function
    processed_log, save_log = load_and_update_processed_log()

    # Check if this user folder is already in the log
    folder_key = os.path.basename(user_folder)
    if folder_key not in processed_log:
        processed_log[folder_key] = {}

    time_folder = os.path.join(user_folder, 'RecordedVideo')
    thermal_folder = os.path.join(user_folder, 'thermal')

    # Ensure both folders exist for the user
    if not os.path.exists(time_folder) or not os.path.exists(thermal_folder):
        return

    user_number = os.path.basename(user_folder)
    segmented_folder = os.path.join(user_folder, 'Segmented_Thermal')
    if not os.path.exists(segmented_folder):
        os.makedirs(segmented_folder)

    time_files = sorted([f for f in os.listdir(time_folder) if f.endswith('.mp4')], key=lambda f: parse_time_file(f)['step'])
    thermal_files = sorted([f for f in os.listdir(thermal_folder) if f.endswith('.mp4')])

    # Parse the time files and determine the begin and end times for each step
    steps = []
    for time_file in time_files:
        time_info = parse_time_file(time_file)
        step_id = str(time_info['step'])

        # Check if this step has already been processed for this user
        if step_id in processed_log[folder_key]:
            # Check duration of the time file
            time_file_path = os.path.join(time_folder, time_file)
            video_capture = cv2.VideoCapture(time_file_path)
            frame_count = int(video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = video_capture.get(cv2.CAP_PROP_FPS)
            video_duration = frame_count / fps if fps > 0 else 0
            video_capture.release()
            
            # Check the segmented file's duration
            segmented_file_path = os.path.join(segmented_folder, time_file)
            segmented_capture = cv2.VideoCapture(segmented_file_path)
            segmented_frame_count = int(segmented_capture.get(cv2.CAP_PROP_FRAME_COUNT))
            segmented_fps = segmented_capture.get(cv2.CAP_PROP_FPS)
            segmented_duration = segmented_frame_count / segmented_fps if segmented_fps > 0 else 0
            segmented_capture.release()

            # Reprocess if duration difference > 10% or either duration is 0
            duration_difference = abs(video_duration - segmented_duration)
            if (video_duration == 0 or segmented_duration == 0) or (duration_difference / video_duration > 0.1):
                print(f"Duration mismatch for step {step_id}. Reprocessing...")
            else:
                print(f"Step {step_id} for user {user_number} has already been processed. Skipping...")
                continue

        # Process and determine start and end times
        video_capture = cv2.VideoCapture(os.path.join(time_folder, time_file))
        frame_count = int(video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = video_capture.get(cv2.CAP_PROP_FPS)
        
        if fps == 0:
            continue
        
        video_duration = frame_count / fps
        video_capture.release()

        # Begin time is now determined by subtracting the video duration from the end time
        time_info['end_time'] = time_info['start_time'] + datetime.timedelta(seconds=video_duration)
        steps.append(time_info)

    # Sort thermal files by extracting the timestamp information from the file names
    try:
        thermal_files = sorted(thermal_files, key=lambda f: parse_thermal_file(os.path.join(thermal_folder, f))['start_time'])
    except Exception as e:
        print(f"Error sorting thermal files: {e}")
        return None

    # Segment all thermal videos for the user
    for thermal_file in thermal_files:
        thermal_file_path = os.path.join(thermal_folder, thermal_file)
        segment_video(thermal_file_path, steps, segmented_folder, user_number, processed_log, step_numbers)

    # Save log after all processing is completed
    save_log()
    print(f"Folder {user_folder} processed and recorded in log.")
#%%
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from rich.progress import track

# Define the main processing function
def process_all_folders(main_folder, user_folders_to_process, step_numbers=[23], parallel=True):
    def process_single_folder(user_folder):
        try:
            user_folder_path = os.path.join(main_folder, user_folder)
            process_user_folder(user_folder_path, step_numbers=step_numbers)
        except Exception as e:
            print(f"Error processing folder {user_folder}: {e}")

    if parallel:
        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=4) as executor:
            results = list(track(
                executor.map(process_single_folder, user_folders_to_process),
                total=len(user_folders_to_process),
                description="Processing Folders"
            ))
    else:
        # Sequential processing with progress tracking
        for user_folder in track(user_folders_to_process, description="Processing Folders"):
            process_single_folder(user_folder)

# List of user numbers to process (e.g., [5, 6, 7]). If empty, process all user folders.
user_numbers_to_process = [12,14,31,35,41,45,47,59,62,65,73,87,100,106] #list(range(1, 150))

# Orignal Thermal File is Missing:  14, 31, 35, 41,45,47,59, 62, 65, 73, 87, 100, 106
# Check if the user_numbers_to_process list is empty
if user_numbers_to_process:
    # Convert user numbers to folder names with zero-padding (e.g., '005', '006', '007')
    user_folders_to_process = [f"{num:03d}" for num in user_numbers_to_process]
else:
    # If no specific user numbers are provided, process all available user folders in the main folder
    user_folders_to_process = sorted(
        [f for f in os.listdir(main_folder) if os.path.isdir(os.path.join(main_folder, f))]
    )


process_all_folders(main_folder, user_folders_to_process, parallel=False)  # Set parallel to False for sequential processing

#%% md
#### Manual Flipp Video
#%%
import os

user_numbers_to_process = [11
                            ,16
                            ,20
                            ,21
                            ,22
                            ,28
                            ,29
                            ,30
                            ,36
                            ,39
                            ,40
                            ,49
                            ,51
                            ,52
                            ,53
                            ,60
                            ,61
                            ,66
                            ,67
                            ,68
                            ,72
                            ,78
                            ,81
                            ,82
                            ,83
                            ,86
                            ,94
                            ,115]

# Check if the user_numbers_to_process list is empty
if user_numbers_to_process:
    # Convert user numbers to folder names with zero-padding (e.g., '005', '006', '007')
    user_folders_to_process = [f"{num:03d}" for num in user_numbers_to_process]

for user_folder in user_folders_to_process:
    segmented_thermal_folder = os.path.join(main_folder, user_folder, 'Segmented_Thermal')
    segmented_thermal_files = []
    if os.path.exists(segmented_thermal_folder):
        segmented_thermal_files = sorted([f for f in os.listdir(segmented_thermal_folder) if f.endswith('.mp4')], key=lambda f: parse_time_file(f)['step'])

    # Parse the time files and determine the begin and end times for each step
    steps = []
    for input_video_path in segmented_thermal_files:
        output_video_path = "rotated_"+ input_video_path
        full_output_video_path = os.path.join(main_folder, user_folder, 'Segmented_Thermal', output_video_path)
        full_input_video_path = os.path.join(main_folder, user_folder, 'Segmented_Thermal', input_video_path)
        command = [
            'ffmpeg', '-y', '-i', full_input_video_path, '-vf', 'vflip,hflip',
            '-c:v', 'libx264', '-an', full_output_video_path
        ]
        subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        if os.path.exists(full_output_video_path):
            os.replace(full_output_video_path, full_input_video_path)

        # Clean up temporary files
        if os.path.exists(full_output_video_path):
            os.remove(full_output_video_path)
        
        print(f"{input_video_path} completed")

#%% md
#### Manual Rotate Video
#%%
import os

user_numbers_to_process = [6,7]

# Check if the user_numbers_to_process list is empty
if user_numbers_to_process:
    # Convert user numbers to folder names with zero-padding (e.g., '005', '006', '007')
    user_folders_to_process = [f"{num:03d}" for num in user_numbers_to_process]

for user_folder in user_folders_to_process:
    segmented_thermal_folder = os.path.join(main_folder, user_folder, 'Segmented_Thermal')
    segmented_thermal_files = []
    if os.path.exists(segmented_thermal_folder):
        segmented_thermal_files = sorted([f for f in os.listdir(segmented_thermal_folder) if f.endswith('.mp4')], key=lambda f: parse_time_file(f)['step'])

    # Parse the time files and determine the begin and end times for each step
    steps = []
    for input_video_path in segmented_thermal_files:
        output_video_path = "rotated_"+ input_video_path
        full_output_video_path = os.path.join(main_folder, user_folder, 'Segmented_Thermal', output_video_path)
        full_input_video_path = os.path.join(main_folder, user_folder, 'Segmented_Thermal', input_video_path)
        command = [
            'ffmpeg', '-y', '-i', full_input_video_path, '-vf', 'transpose=1',
            '-c:v', 'libx264', '-an', full_output_video_path
        ]
        subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        if os.path.exists(full_output_video_path):
            os.replace(full_output_video_path, full_input_video_path)

        # Clean up temporary files
        if os.path.exists(full_output_video_path):
            os.remove(full_output_video_path)

#%% md
### Single File
#%% md
<div><img src="../resources/images/facial_roi.jpg" width="200"/></div>
#%%
from IPython.core.display import Video
location = "../resources/videos/thermal_participant_1.mp4"
Video(location, embed=False, height=400)
#%%
# import the necessary packages
from imutils import paths
from imutils import face_utils
import imutils
import numpy as np
import dlib
import cv2
import os
import matplotlib.pyplot as plt

# 如果想要旋转图像的话，见researchtoolbox.thermal_imaging.utils

class features_extraction:
    def __init__(self, model_path, input_file_path, output_file_path, para_dict):
        cp = Path()
        self.model_path = model_path
        self.input_file_path = input_file_path
        self.output_mp4_path = os.path.join(output_file_path,"video_output")
        self.no_rectangle_image_path = os.path.join(output_file_path,"test_images_output")
        self.visualization_path = os.path.join(output_file_path,"visualization_output")
        self.output_csv_path =  os.path.join(output_file_path,"csv_output")

        cp.check_path_or_create(self.model_path)
        cp.check_path_or_create(self.input_file_path)
        cp.check_path_or_create(self.output_mp4_path)
        cp.check_path_or_create(self.no_rectangle_image_path)
        cp.check_path_or_create(self.output_csv_path)
        cp.check_path_or_create(self.visualization_path)

        self.n_feature_points = para_dict.get("n_feature_points", 54+7)
        self.n_sampling = para_dict.get("n_sampling", 30)
        self.resize_height = para_dict.get("resize_height", 600)
        self.upsampling_times = para_dict.get("upsampling_times", 1)
        self.minimum_gray_color = para_dict.get("minimum_gray_color", 30)

        self.height_lower_threshold = np.int16(self.resize_height*0.2)
        self.height_upper_threshold = np.int16(self.resize_height * 0.8)
        self.adaptive_length = np.int16(30 * 10 / self.n_sampling)  # 多少禎以後，限制數值變化的範圍

        # load the face detector (HOG-SVM)
        print("[INFO] loading dlib thermal face detector...")
        self.detector = dlib.simple_object_detector(os.path.join(self.model_path, "dlib_face_detector.svm"))

        # load the facial landmarks predictor
        print("[INFO] loading facial landmark predictor...")
        self.predictor = dlib.shape_predictor(os.path.join(self.model_path, "dlib_landmark_predictor.dat"))

    def folder_process(self):



        video_files = list(paths.list_files(self.input_file_path))
        # loop over the images
        for ind, input_file_fullname in enumerate(video_files, 1):
            print("[INFO] Processing video: {}/{}".format(ind, len(video_files)))
            self.file_process(input_file_fullname)



        print('finished')

    def file_process(self,input_file_fullname):
        n_error = 0
        file_name = os.path.splitext(os.path.basename(input_file_fullname))[0]
        #output_file_fullname = self.output_mp4_path + "output_" + file_name + ".mp4"
        output_file_fullname = os.path.join(self.output_mp4_path,  "output_" + file_name + ".mp4")
        output_csv_file_fullname = os.path.join(self.output_csv_path, file_name + ".csv")

        # initialize the video stream
        vs = cv2.VideoCapture(input_file_fullname)
        length = int(vs.get(cv2. CAP_PROP_FRAME_COUNT))
        # initialize the video writer
        writer = None
        (W, H) = (None, None)
        gray_list = []
        gray_list_for_chart = []
        # 取得平均值 54+7維
        average_list = np.zeros(self.n_feature_points)
        # 禎數
        n_frame = 0
        n_grab = 0
        # 取得平均寛度
        rec_height_list = []
        x_list = []
        y_list = []
        previous_gray_sub_list = np.zeros(self.n_feature_points)
        while True:
            #print(str(n_grab)+" out of "+str(length))
            # read the next frame from the file
            (grabbed, raw_frame) = vs.read()
            n_grab = n_grab+1

            # break the loop if the frame
            # was not grabbed
            if not grabbed:
                break

            if np.remainder(n_grab, self.n_sampling) != 0:  # 说明不是要抓取的帧
                continue

            # resize the frame
            frame = imutils.resize(raw_frame, height=self.resize_height)

            # copy the frame
            frame_copy = frame.copy()

            # convert the frame to grayscale
            frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2GRAY)

            # detect faces in the frame
            rects = self.detector(frame, upsample_num_times=self.upsampling_times)

            if len(rec_height_list) == self.adaptive_length:
                height_lower_threshold = np.median(rec_height_list[-1*self.adaptive_length:])*0.7
                height_upper_threshold = np.median(rec_height_list[-1*self.adaptive_length:])*1.3
                x_average = np.median(x_list[-1*self.adaptive_length:])
                y_average = np.median(y_list[-1*self.adaptive_length:])

            # loop over the bounding boxes
            if len(rects) > 0:
                for rect in rects:  # 抓取多张脸
                    # if len(rects) >= 1:
                    # rect = rects[0]  # 只抓取1张脸
                    # convert the dlib rectangle into an OpenCV bounding box
                    (x, y, w, h) = face_utils.rect_to_bb(rect)

                    if len(rec_height_list) < self.adaptive_length:
                        y_average = y

                    y_list.append(y)
                    x_list.append(x)
                    if self.height_lower_threshold < h < self.height_upper_threshold and np.abs(y-y_average)/y < 0.5*h:
                        rec_height_list.append(h)  # 方框只在合規的範圍內調整
                        try:
                            # draw a bounding box surrounding the face
                            cv2.rectangle(frame_copy, (x, y), (x + w, y + h), (0, 255, 0), 2)

                            # predict the location of facial landmark coordinates then
                            # convert the prediction to an easily parsable NumPy array
                            shape = self.predictor(frame, rect)
                            shape = face_utils.shape_to_np(shape)

                            # 左側臉頰加點
                            point_4_x = shape[3][0]
                            point_4_y = shape[3][1]
                            point_32_x = shape[31][0]
                            point_32_y = shape[31][1]
                            point_left_cheek_x = np.int16(0.7*point_4_x+0.3*point_32_x)
                            point_left_cheek_y = np.int16(0.7*point_4_y+0.3*point_32_y)
                            shape = np.vstack([shape, [point_left_cheek_x, point_left_cheek_y]])
                            point_left_nose_x = np.int16(0.2*point_4_x+0.8*point_32_x)
                            point_left_nose_y = np.int16(0.2*point_4_y+0.8*point_32_y)
                            shape = np.vstack([shape, [point_left_nose_x, point_left_nose_y]])
                            # 右側臉頰加點
                            point_14_x = shape[13][0]
                            point_14_y = shape[13][1]
                            point_36_x = shape[35][0]
                            point_36_y = shape[35][1]
                            point_right_cheek_x = np.int16(0.7*point_14_x+0.3*point_36_x)
                            point_right_cheek_y = np.int16(0.7*point_14_y+0.3*point_36_y)
                            shape = np.vstack([shape, [point_right_cheek_x, point_right_cheek_y]])
                            point_right_nose_x = np.int16(0.2*point_14_x+0.8*point_36_x)
                            point_right_nose_y = np.int16(0.2*point_14_y+0.8*point_36_y)
                            shape = np.vstack([shape, [point_right_nose_x, point_right_nose_y]])
                            # forehead
                            point_22_x = shape[21][0]
                            point_22_y = shape[21][1]
                            point_23_x = shape[22][0]
                            point_23_y = shape[22][1]
                            point_28_x = shape[27][0]
                            point_28_y = shape[27][1]
                            point_fore_head_x = np.int16(point_22_x + point_23_x - point_28_x)
                            point_fore_head_y = np.int16(0.75*point_22_y + 0.75*point_23_y - 0.5*point_28_y)
                            shape = np.vstack([shape, [point_fore_head_x, point_fore_head_y]])
                            # chin
                            point_9_x = shape[8][0]
                            point_9_y = shape[8][1]
                            point_52_x = shape[51][0]
                            point_52_y = shape[51][1]
                            point_chin_x = np.int16(0.5*point_9_x+0.5*point_52_x)
                            point_chin_y = np.int16(0.5*point_9_y+0.5*point_52_y)
                            shape = np.vstack([shape, [point_chin_x, point_chin_y]])
                            # throat
                            # 臉向右的話,取左側,臉向左,取右側

                            point_52_x = shape[51][0]
                            point_52_y = shape[51][1]
                            if shape[30][0]-shape[27][0] > 0:
                                point_8_x = shape[7][0]
                                point_8_y = shape[7][1]
                                point_throat_x = np.int16(1.4*point_8_x-0.4*point_52_x)
                                point_throat_y = np.int16(1.4*point_8_y-0.4*point_52_y)
                            else:
                                point_10_x = shape[9][0]
                                point_10_y = shape[9][1]
                                point_throat_x = np.int16(1.4*point_10_x-0.4*point_52_x)
                                point_throat_y = np.int16(1.4*point_10_y-0.4*point_52_y)
                            shape = np.vstack([shape, [point_throat_x, point_throat_y]])
                            gray_sub_list = []

                            n_frame = n_frame+1
                            for i in range(self.n_feature_points):
                                selected_point_y = shape[i][1]
                                selected_point_x = shape[i][0]
                                gray_color = frame[selected_point_y, selected_point_x]
                                # 在adaptive_length之後才開始檢查數值是否在一個合理的範圍
                                if n_frame > self.adaptive_length:
                                    if (np.abs(gray_color-average_list[i])/average_list[i]) < 0.5:
                                        gray_sub_list.append(gray_color)
                                        average_list[i] = (average_list[i]*(n_frame-1)+gray_color)/n_frame  # 更新平均值
                                    else:
                                        if gray_color < self.minimum_gray_color:
                                            # 小於最低值的,捨棄改用minimum_gray_color代替
                                            gray_sub_list.append(average_list[i])
                                            average_list[i] = average_list[i]  # 這句非必要只是容易看
                                        else:
                                            gray_sub_list.append(average_list[i])  # 變動超過範圍的,回傳平均值
                                            average_list[i] = (average_list[i]*(n_frame-1)+gray_color)/n_frame  # 更新平均值
                                else:
                                    if gray_color < self.minimum_gray_color:
                                        # 小於最低值的,捨棄改用minimum_gray_color代替
                                        gray_sub_list.append(self.minimum_gray_color)
                                        average_list[i] = average_list[i]  # 這句非必要只是容易看
                                    else:
                                        gray_sub_list.append(gray_color)
                                        average_list[i] = (average_list[i]*(n_frame-1)+gray_color)/n_frame  # 更新平均值

                            previous_gray_sub_list = gray_sub_list
                            gray_list.append(gray_sub_list)
                            gray_list_for_chart.append(gray_sub_list)

                            # loop over the (x, y)-coordinates from our dlib shape
                            # predictor model draw them on the image
                            for (sx, sy) in shape:
                                cv2.circle(frame_copy, (sx, sy), 2, (255, 0, 0), -1)

                            # 加點
                            cv2.circle(frame_copy, (point_left_cheek_x, point_left_cheek_y), 1, (0, 0, 255), -1)
                            cv2.circle(frame_copy, (point_left_nose_x, point_left_nose_y), 1, (0, 0, 255), -1)
                            cv2.circle(frame_copy, (point_right_cheek_x, point_right_cheek_y), 1, (0, 0, 255), -1)
                            cv2.circle(frame_copy, (point_right_nose_x, point_right_nose_y), 1, (0, 0, 255), -1)
                            cv2.circle(frame_copy, (point_fore_head_x, point_fore_head_y), 1, (0, 0, 255), -1)
                            cv2.circle(frame_copy, (point_chin_x, point_chin_y), 1, (0, 0, 255), -1)
                            cv2.circle(frame_copy, (point_throat_x, point_throat_y), 1, (0, 0, 255), -1)
                        except Exception as inst:
                            n_error = n_error + 1
                            print("error:")
                            print(n_error)
                            print(inst)
                            gray_list_for_chart.append(previous_gray_sub_list)  # 補足frame以畫圖
                    else:
                        print("h out of boundary or y changes")
                        print("upper:" + str(self.height_upper_threshold) + ";h=" + str(h) + ";lower=" + str(self.height_lower_threshold))
                        print("y=" + str(y) + ";y_average=" + str(y_average))
                        gray_list_for_chart.append(previous_gray_sub_list)  # 補足frame以畫圖
            else:
                print("len(rects)==0")
                testing_image_fullname = os.path.join(self.no_rectangle_image_path, str(n_grab) + ".png")
                cv2.imwrite(testing_image_fullname, frame)
                gray_list_for_chart.append(previous_gray_sub_list)  # 補足frame以畫圖

            # if the frame dimensions are empty, grab them
            if W is None or H is None:
                (H, W) = frame.shape[:2]

            # check if the video writer is None
            if writer is None:
                # initialize our video writer
                fourcc = cv2.VideoWriter_fourcc(*"mp4v")
                writer = cv2.VideoWriter(output_file_fullname, fourcc, 28, (frame.shape[1], frame.shape[0]), True)

            # push the frame to the writer
            writer.write(frame_copy)

        if len(gray_list_for_chart) > 0:
            print(file_name)
            # print(gray_list_for_chart)
            plt.rcParams["figure.figsize"] = (15, 12)
            plt.title(file_name)
            plt.ylim([0, 255])
            rolling_n = 10  # default=10
            import pandas as pd
            row_21 = pd.DataFrame([row[21] for row in gray_list_for_chart]).rolling(rolling_n).mean()[rolling_n:]
            row_22 = pd.DataFrame([row[22] for row in gray_list_for_chart]).rolling(rolling_n).mean()[rolling_n:]
            row_58 = pd.DataFrame([row[58] for row in gray_list_for_chart]).rolling(rolling_n).mean()[rolling_n:]
            row_29 = pd.DataFrame([row[29] for row in gray_list_for_chart]).rolling(rolling_n).mean()[rolling_n:]
            row_30 = pd.DataFrame([row[30] for row in gray_list_for_chart]).rolling(rolling_n).mean()[rolling_n:]
            row_54 = pd.DataFrame([row[54] for row in gray_list_for_chart]).rolling(rolling_n).mean()[rolling_n:]
            row_55 = pd.DataFrame([row[55] for row in gray_list_for_chart]).rolling(rolling_n).mean()[rolling_n:]
            row_59 = pd.DataFrame([row[59] for row in gray_list_for_chart]).rolling(rolling_n).mean()[rolling_n:]
            row_60 = pd.DataFrame([row[60] for row in gray_list_for_chart]).rolling(rolling_n).mean()[rolling_n:]

            plt.plot(row_21, label="Left Eyebrow")
            plt.plot(row_22, label="Right Eyebrow")
            plt.plot(row_58, label="Fore Head")
            plt.plot(row_29, label="Next To Nose Tip")
            plt.plot(row_30, label="Nose Tip")
            plt.plot(row_54, label="Left Cheek")
            plt.plot(row_55, label="Left Nose")
            plt.plot(row_59, label="Chin")
            plt.plot(row_60, label="Throat")
            # print(row_21.to_string(), row_22, row_58, row_29, row_30,  row_54, row_55, row_59, row_60)
            # a = [row[21] for row in gray_list_for_chart]
            # print(a)
            # print(pd.DataFrame(a).rolling(10).mean()[10:])
            # plt.legend(loc="upper left")
            plt.legend(bbox_to_anchor=(1.05, 1.0), loc='upper left')
            # plt.title('test')
            plt.tight_layout()
            output_image_fullname = os.path.join(os.getcwd(), self.visualization_path, file_name + ".png")
            plt.savefig(output_image_fullname)
            fig1 = plt.figure(file_name)
            # plt.show()
            print(os.path.join(os.getcwd(), output_csv_file_fullname))
            np.savetxt(os.path.join(os.getcwd(), output_csv_file_fullname), gray_list, delimiter=",")

            vs.release()
            writer.release()
            # do a bit cleanup
            cv2.destroyAllWindows()
            
            return np.array(gray_list), [f"thermal_feature_{i}" for i in range(61)]

    def dataframe_to_list_of_lists(self, df):
        """
        Converts a DataFrame into a list of lists and returns both the list and the column names.

        Parameters:
        df (pd.DataFrame): The input DataFrame to convert.

        Returns:
        tuple: A tuple containing:
            - list of lists (each sublist is a row from the DataFrame)
            - list of column names
        """
        # Convert DataFrame to list of lists
        list_of_lists = df.values.tolist()
        
        # Get column names as a list
        column_names = df.columns.tolist()
        
        return np.array(list_of_lists), column_names
    
    def summarized_file_process(self,input_file_fullname):

        import psychai.feature.feature_extraction.feature_processor

        # Extract features using multiple strategies and plot them
        strategies = ['mean', 'variance', 'max', 'end_to_begin']

        features, feature_names = self.file_process(input_file_fullname)

        # Summarize features using the specified strategies
        processor = psychai.feature.feature_extraction.feature_processor.FeatureProcessor()
        summarized_features, summarized_feature_names = processor.summarize_features(features, strategies, feature_names)
        # Append the element to the array
        # summarized_features = np.append(summarized_features, input_file_fullname)
        # summarized_feature_names.append("files") 
        return summarized_features, summarized_feature_names        
#%%
input_file_path = "../resources/thermal"  # 实际上是视频
model_path = "../resources/models/thermal"
output_file_path = "../results/thermal"
upsampling_times = 1
n_feature_points = 54+7  # 臉部特徵點的數量
n_sampling = 30  # downsampling
adaptive_length = np.int16(30*10/n_sampling)  # 多少禎以後，限制數值變化的範圍
resize_height = 600  # 調整影像的大小。600時，大概每秒可以處理1禎，但是如果下降到300，只需要1/100的時間。不過，識別正確性會減低。
height_lower_threshold = np.int16(resize_height*0.2)
height_upper_threshold = np.int16(resize_height*0.8)
x_average = 0
y_average = 0
minimum_gray_color = 30  # 設一個最低的值,以避免抓到奇怪的數值

para_dict = {
    "n_feature_points": n_feature_points,
    "n_sampling": n_sampling,
    "resize_height": resize_height,
    "upsampling_times": upsampling_times,
    "minimum_gray_color": minimum_gray_color
}

feature_extractor = features_extraction(model_path, input_file_path, output_file_path, para_dict)
#%%
import psychai.feature.feature_extraction.feature_processor

file = r'D:\Programming3\psychai\example\paper5_mmer_moral\resources\raw_data\005\Segmented_Thermal\PS-9_005_23_05_25_13_10_35.mp4'
features, feature_names = feature_extractor.file_process(file)

# Extract features using multiple strategies and plot them
strategies = ['mean', 'variance', 'max', 'end_to_begin']
processor = psychai.feature.feature_extraction.feature_processor.FeatureProcessor()

summarized_features, summarized_feature_names = processor.summarize_features(features, strategies, feature_names)
#%%
file = r'D:\Programming3\psychai\example\paper5_mmer_moral\resources\raw_data\005\Segmented_Thermal\PS-9_005_23_05_25_13_10_35.mp4'
summarized_features, summarized_feature_names  = feature_extractor.summarized_file_process(file)
#%% md
### Folder
#%%
# base_directory = r"D:\Programming3\psychai\example\paper5_mmer_moral\resources\raw_data"
base_directory = r"K:\Backup\Experiments\Moral Elevation\Disk1_2_combined\FS-9 Day 1"

# Create a FileFilter instance with multiple attribute filters
filters = [(2, '23')]
file_filter = utilities.FileFilter(base_directory, attribute_filters=filters, modality_value='Segmented_Thermal')

# Get matching files for user IDs from 1 to 13
df = file_filter.get_matching_files(range(1, 150))

# Load the rct.csv file
rct_df = pd.read_csv('../resources/data/rct/rct.csv')

# Merge the two dataframes based on the 'user_id' column
df_updated = pd.merge(df, rct_df[['user_id', 'Group']], on='user_id', how='left')

# Find the row with the largest file_size for each user_id
df_largest = df_updated.loc[df_updated.groupby('user_id')['file_size'].idxmax()]

# Reset the index if needed
df_largest.reset_index(drop=True, inplace=True)

# Display the result
print(df_largest)
#%%
import psychai.feature.feature_extraction.feature_retriever as retriever
#%%
# Instantiate FeatureProcessor with the custom feature extractors
processor = retriever.FeatureRetriever(df_largest, summarized_feature_extractors=[feature_extractor.summarized_file_process]
                             ,cache_file_path="../results/thermal/thermal_features_cache.pkl")
#%%
# Extract features and return the final DataFrame
df_with_features = processor.extract_features()
#%%
import pandas as pd
from IPython.display import display

# Set display options for full DataFrame output
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', None)

display(df_with_features)

df_with_features.to_csv(r"D:\Programming3\psychai\example\paper5_mmer_moral\results\thermal\summarized_results\df_with_features.csv")

#%% md
## Footnote
- Copyright：Ivan Liu 
- Last Update: 2024
- Env：course_9 for feature extraction, psychai241104 for emotion recognition
- Notes:
    - install dlib by "conda install -c conda-forge dlib" reference :https://pyimagesearch.com/2018/01/22/install-dlib-easy-complete-guide/
    - Usage: \$ python dlib_predict_video.py --input video/2_0.avi --models  models/ --upsample 1 --output demo/output.mp4
- References:
    - https://github.com/IS2AI/thermal-facial-landmarks-detection