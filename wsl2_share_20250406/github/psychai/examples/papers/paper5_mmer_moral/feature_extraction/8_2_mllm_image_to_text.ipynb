#%% md
# Feature Extraction - LLM
#%% md
## Feature Extraction
#%%
import os
import torch
import requests
from PIL import Image
from IPython.display import Image as Image_Show
import subprocess
import matplotlib.pyplot as plt
import pandas as pd
from transformers import MllamaForConditionalGeneration, AutoProcessor, BitsAndBytesConfig
from peft import LoraConfig, get_peft_model
from datasets import load_dataset, load_from_disk

huggingface_cache_location = os.path.abspath("../../../" + os.getenv("huggingface_cache_location"))
output_csv_path = "../results/mmllm/csv_output/results.csv"
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model_id = "meta-llama/Llama-3.2-11B-Vision-Instruct"
#%%
def get_gpu_memory_usage():
    result = subprocess.run(
        ["nvidia-smi", "--query-gpu=memory.used,memory.total", "--format=csv,nounits,noheader"],
        stdout=subprocess.PIPE
    )
    memory_usage = result.stdout.decode("utf-8").strip().split('\n')
    for i, memory in enumerate(memory_usage):
        used, total = memory.split(',')
        print(f"GPU {i}: {used.strip()} MiB / {total.strip()} MiB used")
#%%
huggingface_cache_location
#%%
processor = AutoProcessor.from_pretrained(model_id, 
                                          cache_dir=huggingface_cache_location, 
                                          local_files_only=True)
#%%
bnb_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_quant_type="nf4",
    bnb_4bit_compute_dtype=torch.bfloat16
)
 
model_quantized = MllamaForConditionalGeneration.from_pretrained(
    model_id,
    quantization_config=bnb_config,
    cache_dir=huggingface_cache_location,
    local_files_only=True
)
#%%
messages = [
    {"role": "user", "content": [
        {"type": "image"},
        {"type": "text", "text": """
            Use the following cues to describe a person in an image who may appear compassionate, inspired, both, or neither.

            Keep the answer in 200 words.
         
            [Facial Features]

            Soft Eyes: Describe if the person’s eyes seem warm and slightly squinted, with relaxed muscles around them, conveying empathy and warmth. Look for a gentle and welcoming expression.
            Gentle Smile: Note if their smile is soft and genuine, rather than a broad grin, and whether it reaches their eyes, creating crow’s feet, indicating sincerity and warmth.
            Raised Eyebrows: Check if their eyebrows are slightly raised, which can reflect openness and curiosity, suggesting inspiration or attentiveness.
            Relaxed Mouth: Observe if their mouth is slightly open, perhaps indicating awe, or gently pressed, conveying care or understanding.
            Head Tilt: Note any slight tilt of their head, a cue often associated with attentiveness, warmth, and openness to listening.
            [Pose Features]

            Open Body Language: Describe if their arms are uncrossed and relaxed, perhaps hanging comfortably at their sides or gently clasped, signaling approachability and openness.
            Forward Lean: Notice if they lean slightly forward, which often signifies engagement, empathy, or eagerness to connect.
            Gentle Gestures: If gestures are visible, assess if they are smooth and slow, possibly with open palms—a welcoming, trustworthy signal.
            Hand on Heart or Chest: Look for any gesture where they place a hand on their chest, indicating sincerity and a personal connection to their emotions.
            Relaxed Stance: Describe their posture. A relaxed yet alert stance, with slightly bent knees or relaxed shoulders, can suggest comfort, openness, and emotional receptiveness.
         """
         }
    ]}
]

input_text = processor.apply_chat_template(
    messages, add_generation_prompt=True,
)
#%% md
### Folder Exampe
#%%
folder_path = "../results/mmllm/frame_output"
#%% md
### Sequencial
#%%
import os
import pandas as pd
from PIL import Image
from tqdm import tqdm
def print_time(description, video_file_base_name,  start):

    # End the timer
    end = time.time()

    # Calculate the elapsed time
    elapsed_time = end - start

    # Convert to seconds, minutes, and hours
    seconds = int(elapsed_time % 60)
    minutes = int((elapsed_time // 60) % 60)
    hours = int(elapsed_time // 3600)

    # Print the result in a readable format
    print(f"[File:{video_file_base_name}] {description}. Elapsed time: {hours} hours, {minutes} minutes, {seconds} seconds")

import time


# Load existing results if they exist
def load_existing_results(output_csv_path):
    if os.path.exists(output_csv_path):
        return pd.read_csv(output_csv_path, sep='\t')
    else:
        return pd.DataFrame(columns=["file_path", "sequence", "decoded_text"])
    


# Function to process a single file
def process_file(image_path, input_text, processor, model_quantized, existing_results,max_new_tokens=100, override=False):
    try:
        start = time.time()
        # Extract file path and sequence number
        base_name = os.path.basename(image_path)
        file_path = base_name.split("_frame_")[0] + ".mp4"
        sequence = int(base_name.split("_frame_")[-1].split(".")[0])

        # Check if result already exists
        if not override:
            if not existing_results.empty:
                result = existing_results[(existing_results['file_path'] == file_path) & (existing_results['sequence'] == sequence)]
                if not result.empty:
                    # Return existing decoded_text without processing
                    return result.iloc[0].to_dict()
        
        # Process the image if decoded_text doesn't exist
        image = Image.open(image_path)
        inputs = processor(
            image,
            input_text,
            add_special_tokens=False,
            return_tensors="pt"
        ).to(model_quantized.device)
        
        # Generate model output
        output = model_quantized.generate(**inputs, max_new_tokens=200)
        decoded_text = processor.decode(output[0][inputs["input_ids"].shape[-1]:])
        print_time("Input Decoded.", base_name , start)
        print(f"file:{file_path}\nmessage:{decoded_text}")
        # Return the new result
        return {"file_path": file_path, "sequence": sequence, "decoded_text": decoded_text}
    
    except Exception as e:
        print(f"Failed to process {image_path}: {e}")
        return None

# Main function to process all files one by one
def process_files_sequentially(folder_path, input_text, processor, model_quantized, output_csv_path,max_new_tokens, override):
    # Load existing results
    existing_results = load_existing_results(output_csv_path)
    results = []

    # List all image files in the folder
    image_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith(".jpg")]

    # Process each file one by one and track progress with tqdm
    for image_path in tqdm(image_files, desc="Processing files"):
        result = process_file(image_path, input_text, processor, model_quantized, existing_results, max_new_tokens, override)
        if result:
            results.append(result)

    # Create a DataFrame from results
    if results:
        df_new_results = pd.DataFrame(results)
        # Combine new results with existing results
        df_combined = pd.concat([existing_results, df_new_results]).drop_duplicates(subset=["file_path", "sequence"]).reset_index(drop=True)
        # Save the combined results to CSV
        df_combined.to_csv(output_csv_path, sep='\t', index=False)
        print(f"Results saved to {output_csv_path}")

# Example usage
# Define input_text, processor, and model_quantized
# input_text = "Your input text here"
# processor = YourProcessorClass()  # Replace with actual processor instance
# model_quantized = YourModelClass()  # Replace with actual model instance

# process_files_sequentially(folder_path, input_text, processor, model_quantized, output_csv_path)

#%%
process_files_sequentially(folder_path, input_text, processor, model_quantized, output_csv_path, 400, False)
#%% md
### Single Image Exampe
#%%
image_path = r"C:\Working\Programming\Python\psychai_v1\example\paper5_mmer_moral\results\mmllm\frame_output\PS-9_001_23_05_21_12_42_54_frame_1.jpg"
Image_Show(image_path, width=400)
#%%
output_csv_path = r"C:\Working\Programming\Python\psychai_v1\example\paper5_mmer_moral\results\mmllm\csv_output"

result = process_file(image_path, input_text, processor, model_quantized, output_csv_path, pd.DataFrame())
#%%
result
#%% md
## Footnote
- Copyright：Ivan Liu 
- Last Update: 2024
- Env：psychai241104
- References: 
    - https://github.com/PrudhviGNV/Speech-Emotion-Recognization