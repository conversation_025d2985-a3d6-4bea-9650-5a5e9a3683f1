#%% md
# Remote PPG
#%% md
## Feature Extraction
#%% md
### Environment Preparation
#%%
import sys
import os
from importlib import import_module, util
import matplotlib.pyplot as plt
import numpy as np
from pyVHR.extraction.sig_processing import *
from pyVHR.extraction.sig_extraction_methods import *
from pyVHR.extraction.skin_extraction_methods import *
from pyVHR.BVP.BVP import *
from pyVHR.BPM.BPM import *
from pyVHR.BVP.methods import *
from pyVHR.plot.visualize import visualize_landmarks_list
import shutil
import csv
import time
from datetime import datetime
import plotly.express as px
from inspect import getmembers, isfunction

# Get the parent directory
# This is useful if you need to import modules from a parent directory.
import sys
parent_dir = os.path.abspath('../')
sys.path.append(parent_dir)

# Import any custom utilities from the parent directory (if needed)
import utilities

import pandas as pd
#%%
project_path = os.path.abspath("../../../")
if str(project_path) not in sys.path:
    sys.path.insert(0, str(project_path))
    print(f"Added {project_path} to sys.path")
else:
    print(f"{project_path} is already in sys.path")

from psychai_v1.remote_ppg.vhr.filters import *
from psychai_v1.remote_ppg.vhr.signal_process import SignalProcessing
#%%
roi_approach="patches"
roi_method="faceparsing"
cuda=True
method='cupy_POS'
bpm_type='welch'
pre_filt=False
post_filt=True
verb=True

to_recalculate = False #是否重新建計算而不要用舊的結果
debug_mode = False
length_of_windows = 12



input_path =  r'D:\Programming3\psychai\example\paper5_mmer_moral\results\remote_ppg'
target_file_path =  r'D:\Programming3\psychai\example\paper5_mmer_moral\results\remote_ppg\finished_video'
image_output_path = r'D:\Programming3\psychai\example\paper5_mmer_moral\results\remote_ppg\image_output'
sig_output_path = r'D:\Programming3\psychai\example\paper5_mmer_moral\results\remote_ppg\sig_output'
bvps_output_path = r'D:\Programming3\psychai\example\paper5_mmer_moral\results\remote_ppg\bvps_results'
bpmES_output_path = r'D:\Programming3\psychai\example\paper5_mmer_moral\results\remote_ppg\bpmES_results'
bpm_output_path = r'D:\Programming3\psychai\example\paper5_mmer_moral\results\remote_ppg\bpm_results'
file_name = r'D:\Programming3\psychai\example\paper5_mmer_moral\resources\images\face_example.jpg' #測試用
testing_video_file = r'D:\Programming3\psychai\example\paper5_mmer_moral\resources\videos\thermal_participant_1.mp4'
extension = "mp4"

do_image_extraction = False
#%%
#100个特征点
# 所有特征点的定义可以参考:

# https://google.github.io/mediapipe/solutions/face_mesh.html
# https://raw.githubusercontent.com/google/mediapipe/a908d668c730da128dfa8d9f6bd25d519d006692/mediapipe/modules/face_geometry/data/canonical_face_model_uv_visualization.png
# https://github.com/tensorflow/tfjs-models/blob/838611c02f51159afdd77469ce67f0e26b7bbb23/face-landmarks-detection/src/mediapipe-facemesh/keypoints.ts

landmarks_list = [3, 4, 5, 6, 8, 9, 10, 18, 32, 36, 43, 48, 50, 67, 69, 92, 101, 103, 104, 108, 109, 123, 134, 135, 142, 151, 164, 167, 182, 187, 192, 197, 201, 205, 206, 207, 210, 211, 212, 216, 248, 262, 266, 273, 278, 280, 297, 299, 322, 330, 332, 333, 337, 338, 352,  363, 364, 371, 393, 406, 411, 416, 421, 425, 426, 427, 430,431, 432, 436]
ldmks_list_A = [2, 3, 4, 5, 6, 8, 9, 10, 18, 21, 32, 35, 36, 43, 46, 47, 48, 50, 54, 58, 67, 68, 69, 71, 92, 93, 101, 103, 104, 108, 109, 116, 117, 118, 123, 132, 134, 135, 138, 139, 142, 148, 149, 150, 151, 152, 182, 187, 188, 193, 197, 201, 205, 206, 207, 210, 211, 212, 216, 234, 248, 251, 262, 265, 266, 273, 277, 278, 280, 284, 288, 297, 299, 322, 323, 330, 332, 333, 337, 338, 345, 346, 361, 363, 364, 367, 368, 371, 377, 379, 411, 412, 417, 421, 425, 426, 427, 430, 432, 436]
ldmks_list_C = [2]
ldmks_list_B = [3, 4, 5, 6, 8, 9, 10, 18, 32, 36, 43, 48, 50, 67, 69, 92, 101, 103, 104, 108, 109, 123, 134, 135, 142, 151, 164, 167, 182, 187, 192, 197, 201, 205, 206, 207, 210, 211, 212, 216, 248, 262, 266, 273, 278, 280, 297, 299, 322, 330, 332, 333, 337, 338, 352,  363, 364, 371, 393, 406, 411, 416, 421, 425, 426, 427, 430,431, 432, 436]
ldmks_list_choice = "B"
ldmks_list = ldmks_list_B

sig_processing = SignalProcessing()
av_meths = getmembers(pyVHR.BVP.methods, isfunction)
available_methods = [am[0] for am in av_meths]
assert method in available_methods, "\nrPPG method not recognized!!"
#%% md
### Image Feature
#%%
# sig_processing = SignalProcessing()
# av_meths = getmembers(pyVHR.BVP.methods, isfunction)
# available_methods = [am[0] for am in av_meths]



#檢查landmark點用
PRESENCE_THRESHOLD = 0.5
VISIBILITY_THRESHOLD = 0.5

if do_image_extraction:

    imag = cv2.imread(file_name, cv2.COLOR_RGB2BGR)
    imag = cv2.cvtColor(imag, cv2.COLOR_BGR2RGB)
    mp_drawing = mp.solutions.drawing_utils
    mp_face_mesh = mp.solutions.face_mesh
    with mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            min_detection_confidence=0.5) as face_mesh:
        image = cv2.imread(file_name)
        results = face_mesh.process(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        width = image.shape[1]
        height = image.shape[0]
        face_landmarks = results.multi_face_landmarks[0]
        ldmks = np.zeros((468, 3), dtype=np.float32)
        for idx, landmark in enumerate(face_landmarks.landmark):
            if ((landmark.HasField('visibility') and landmark.visibility < VISIBILITY_THRESHOLD)
                    or (landmark.HasField('presence') and landmark.presence < PRESENCE_THRESHOLD)):
                ldmks[idx, 0] = -1.0
                ldmks[idx, 1] = -1.0
                ldmks[idx, 2] = -1.0
            else:
                coords = mp_drawing._normalized_to_pixel_coordinates(
                    landmark.x, landmark.y, width, height)
                if coords:
                    ldmks[idx, 0] = coords[0]
                    ldmks[idx, 1] = coords[1]
                    ldmks[idx, 2] = idx
                else:
                    ldmks[idx, 0] = -1.0
                    ldmks[idx, 1] = -1.0
                    ldmks[idx, 2] = -1.0

    filtered_ldmks = []
    if landmarks_list is not None:
        for idx in landmarks_list:
            filtered_ldmks.append(ldmks[idx])
        filtered_ldmks = np.array(filtered_ldmks, dtype=np.float32)
    else:
        filtered_ldmks = ldmks

    fig = px.imshow(imag)
    for l in filtered_ldmks:
        name = '特征_' + str(int(l[2]))
        fig.add_trace(go.Scatter(x=(l[0],), y=(l[1],), name=name, mode='markers', 
                                marker=dict(color='red', size=5)))
    fig.update_xaxes(range=[0,imag.shape[1]])
    fig.update_yaxes(range=[imag.shape[0],0])
    fig.update_layout(paper_bgcolor='#eee') 

#%%
assert method in available_methods, "\nrPPG method not recognized!!"

if cuda:
    sig_processing.display_cuda_device()
    sig_processing.choose_cuda_device(0)

# set skin extractor
target_device = 'GPU' if cuda else 'CPU'
if roi_method == 'convexhull':
    sig_processing.set_skin_extractor(
        SkinExtractionConvexHull(target_device))
elif roi_method == 'faceparsing':
    sig_processing.set_skin_extractor(
        SkinExtractionFaceParsing(target_device))
else:
    raise ValueError("Unknown 'roi_method'")

assert roi_approach == 'patches' or roi_approach=='hol', "\nROI extraction approach not recognized!"

# set patches
if roi_approach == 'patches':
    #ldmks_list = ast.literal_eval(landmarks_list)
    #if len(ldmks_list) > 0:
    sig_processing.set_landmarks(ldmks_list)
    # set squares patches side dimension
    sig_processing.set_square_patches_side(28.0)

# set sig-processing and skin-processing params
SignalProcessingParams.RGB_LOW_TH = 75
SignalProcessingParams.RGB_HIGH_TH = 230
SkinProcessingParams.RGB_LOW_TH = 75
SkinProcessingParams.RGB_HIGH_TH = 230
from os import listdir
from os.path import isfile, join
# video_files = [f for f in listdir(input_path) if isfile(join(input_path, f))]
#%%
def print_time(description, video_file_base_name,  start):

    # End the timer
    end = time.time()

    # Calculate the elapsed time
    elapsed_time = end - start

    # Convert to seconds, minutes, and hours
    seconds = int(elapsed_time % 60)
    minutes = int((elapsed_time // 60) % 60)
    hours = int(elapsed_time // 3600)

    # Print the result in a readable format
    print(f"[File:{video_file_base_name}] {description}. Elapsed time: {hours} hours, {minutes} minutes, {seconds} seconds")
#%%
import pickle, pprint

class RemotePPGFeatureExtractor:
    def __init__(self):
        pass
    def get_bbi_without_moving(self, video_file_full_name):
        return self.get_bbi(video_file_full_name, False)
    
    def get_bbi(self, video_file_full_name, move_file_after_processing):

        video_file_base_name = 	os.path.splitext(os.path.basename(video_file_full_name))[0]
        target_video_file_full_name = os.path.join(target_file_path, video_file_base_name)
        image_output_full_name = os.path.join(image_output_path, video_file_base_name + ".png")
        if os.path.exists(image_output_full_name):
            print(f"[File:{video_file_full_name}] exists. Skip")
            return 
        
        #image_output_full_name = image_output_path + video_file_base_name + ".png"
        if verb:
            now = datetime.now()
            current_time = now.strftime("%H:%M:%S")
            print("Current Time =", current_time)
            # print(f'[Processing Video: ' + video_file_full_name)
            print(f"[File:{video_file_base_name}] Starts. Current Time: {current_time}")

        fps = get_fps(video_file_full_name)
        sig_processing.set_total_frames(0)

        # -- ROI selection
        #檢查sig是否完成
        sig_file_full_name = os.path.join(sig_output_path, video_file_base_name + ".pkl")
        sig = []
        start = time.time()
        if os.path.exists(sig_file_full_name) and not to_recalculate:
            pkl_file = open(sig_file_full_name, 'rb')
            sig = pickle.load(pkl_file)
            pkl_file.close()
        else:
            try: #r_01_05
                if roi_approach == 'hol':
                    # SIG extraction with holistic
                    sig = sig_processing.extract_holistic(video_file_full_name)
                elif roi_approach == 'patches':
                    # SIG extraction with patches
                    sig = sig_processing.extract_patches(video_file_full_name, 'squares', 'mean')
                # if sig != None and len(sig)>0:
                if len(sig)>0:
                    output = open(sig_file_full_name, 'wb')
                    pickle.dump(sig, output)
                    output.close()
                # else:
                #     print(f"error r_01_06: No result generated for {video_file_full_name}")
                #     return
            except Exception as inst:
                print(f"error r_01_05:{sig_file_full_name}")
                print(inst)
                return

        # end = time.time()
        # time_span = end - start
        # print("Signal extraction completed. Landmark type is "+ ldmks_list_choice + ". Time span= "  + str(time_span))
        print_time("Signal extraction completed. Landmark type is "+ ldmks_list_choice, video_file_base_name , start)
        # -- sig windowing
        #len(sig)/fps

        #用6秒分割，再去计算心率，
        windowed_sig, timesES = sig_windowing(sig, length_of_windows , 1, fps)
        print_time(f"Video segmentation completed", video_file_base_name , start)
        #如果不算心率變異, 下面這行可以取回全部
        #windowed_sig, timesES = sig_windowing(sig, np.floor(len(sig)/fps), np.floor(len(sig)/fps), fps)

        # -- PRE FILTERING
        filtered_windowed_sig = windowed_sig

        # -- color threshold - applied only with patches
        if roi_approach == 'patches':
            filtered_windowed_sig = apply_filter(windowed_sig,
                                                    rgb_filter_th,
                                                    params={'RGB_LOW_TH':  75,
                                                            'RGB_HIGH_TH': 230})
            print_time(f"Windows filtered completed", video_file_base_name , start)

        # -- BVP Extraction
        module = import_module('pyVHR.BVP.methods')
        method_to_call = getattr(module, method)
        if 'cpu' in method:
            method_device = 'cpu'
        elif 'torch' in method:
            method_device = 'torch'
        elif 'cupy' in method:
            method_device = 'cuda'

        if 'POS' in method:
            pars = {'fps':'adaptive'}
        elif 'PCA' in method or 'ICA' in method:
            pars = {'component': 'all_comp'}
        else:
            pars = {}
            #


        bvps_file_full_name = os.path.join(bvps_output_path, video_file_base_name + ".pkl")
        #bvps_file_full_name = bvps_output_path + video_file_base_name + ".pkl"
        if os.path.exists(bvps_file_full_name) and not to_recalculate:
            pkl_file = open(bvps_file_full_name, 'rb')
            bvps = pickle.load(pkl_file)
            pkl_file.close()
            print_time("BVPS loaded.", video_file_base_name , start)
        else:
            try: #r_01_02
                # Transform an input RGB windowed signal in a BVP windowed signal using a rPPG method (see pyVHR.BVP.methods).
                bvps = RGB_sig_to_BVP(filtered_windowed_sig, fps,
                                        device_type=method_device, method=method_to_call, params=pars)            
                end = time.time()
                time_span = end - start
                print(time_span)
                output = open(bvps_file_full_name, 'wb')
                pickle.dump(bvps, output)
                output.close()
                print_time(f"Signal to BVP completed", video_file_base_name , start)
            except Exception as inst:
                print("error r_01_02")
                print(inst) 
                return

        # end = time.time()
        # time_span = end - start
        # print(f"Signal to BVP completed:{video_file_base_name}. Time span:{time_span}")
        

        # -- POST FILTERING
        if post_filt:
            try: #r_01_03
                module = import_module('pyVHR.BVP.filters')
                method_to_call = getattr(module, 'BPfilter')
                bvps = apply_filter(bvps, 
                                    method_to_call, 
                                    fps=fps, 
                                    params={'minHz':0.65, 'maxHz':4.0, 'fps':'adaptive', 'order':6})
                print_time(f"Post filter completed", video_file_base_name , start)
            except Exception as inst:
                print("error r_01_03")
                print(inst) 
                return 

        if bpm_type == 'welch':
            if cuda:
                bpmES = BVP_to_BPM_cuda(bvps, fps, minHz=0.65, maxHz=4.0)
            else:
                bpmES = BVP_to_BPM(bvps, fps, minHz=0.65, maxHz=4.0)
            
        elif bpm_type == 'psd_clustering':
            if cuda:
                bpmES = BVP_to_BPM_PSD_clustering_cuda(bvps, fps, minHz=0.65, maxHz=4.0)
            else:
                bpmES = BVP_to_BPM_PSD_clustering(bvps, fps, minHz=0.65, maxHz=4.0)
        else:
            raise ValueError("Unknown 'bpm_type'")
        print_time("BVP to BPM completed", video_file_base_name , start)
        
        # median BPM from multiple estimators BPM
        median_bpmES, mad_bpmES = multi_est_BPM_median(bpmES)
        #print(f"Get median and mad completed:{video_file_base_name}.")
        print_time(f"Get median and mad completed.", video_file_base_name , start)

        import pandas as pd
        bpm_results = pd.DataFrame(
        {'time': timesES,
        'median': median_bpmES,
        'mad': mad_bpmES
        })

        
        plt.title(video_file_base_name)
        #plt.figure()
        plt.plot(timesES, median_bpmES)
        plt.fill_between(timesES, median_bpmES-mad_bpmES, median_bpmES+mad_bpmES, alpha=0.2)
        plt.savefig(image_output_full_name)
        fig1 = plt.figure(video_file_base_name)

        bpmES_file_full_name = os.path.join(bpmES_output_path, video_file_base_name + ".csv")
        #bpmES_file_full_name = bpmES_output_path + video_file_base_name + ".csv"
        try:
            bpmES_frame = pd.DataFrame(bpmES) 
            np.savetxt(bpmES_file_full_name,bpmES_frame,delimiter=",")
        except Exception as inst:
            print("error r_01_07")
            print(inst) 
        bpm_file_full_name = os.path.join(bpm_output_path, video_file_base_name + ".csv")
        #bpm_file_full_name = bpm_output_path + video_file_base_name + ".csv"
        np.savetxt(bpm_file_full_name,bpm_results,delimiter=",")

        if move_file_after_processing:
            shutil.move(video_file_full_name, target_video_file_full_name)

        # end = time.time()
        # time_span = end - start
        # print(f"All processes completed:{video_file_base_name}. Time span:{time_span}")
        print_time(f"All processes completed.", video_file_base_name , start)
        return bpm_results

    # def process_videos(self, video_files, move_file_after_processing):

    #     for ind, video_file_name in enumerate(video_files, 1):
    #         self.process_video(video_file_name, move_file_after_processing)

#%% md
### Single Video to BBI Example
#%%
remote_ppg_extractor = RemotePPGFeatureExtractor()
#%%
# remote_ppg_extractor.get_bbi(testing_video_file,False)
#%% md
### Folder to BBI Example
#%%
single_analysis = utilities.SingleModalityAnalysis()

# base_directory = r"D:\Programming3\psychai\example\paper5_mmer_moral\resources\raw_data"
# base_directory = r"K:\Backup\Experiments\Moral Elevation\Disk1_2_combined\FS-9 Day 1"
base_directory = r"G:\Experiments\Moral Elevation\Disk1_2_combined\FS-9 Day 1"

filters = [(2, '23')]
user_ids = range(1,120)
merging_pdf = pd.read_csv('../resources/data/rct/rct.csv')

df_largest = single_analysis.prosss_folder(base_directory, filters, user_ids, merging_pdf, modality_value="RecordedVideo")
#%%
# df_largest.iloc[[0]]["files"][0]
#%%
# remote_ppg_extractor.get_bbi_without_moving(df_largest.iloc[[0]]["files"][0])
#%% md
#### Sequential
#%%
# for files_row in df_largest["files"]:
#     remote_ppg_extractor.get_bbi_without_moving(files_row)
#%% md
#### Parallel
#%%
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm.notebook import tqdm

with ThreadPoolExecutor() as executor:
    # Submit tasks to the thread pool for each item (file or data identifier)
    futures = {executor.submit(remote_ppg_extractor.get_bbi_without_moving, item): item for item in df_largest["files"]}
    
    # Track the progress using tqdm, and collect results as they are completed
    for future in tqdm(as_completed(futures), total=len(futures), desc="Extracting features"):
        try:
            result_series = future.result()
            result_df = result_series.to_frame().T
            df_results = pd.concat([df_results, result_df], ignore_index=True)
        except Exception as e:
            print(f"Error processing item {futures[future]}: {e}")
#%% md
# 附錄
- 版權：Ivan Liu 最後更新時間 2024
- 虛擬環境：course_12
- 內容來源（本教學的部分內容改編自）

    #这个内容是来自github pyVHR https://github.com/phuselab/pyVHR

    #安装时用conda可以把dependency载下来

    #conda env create --file https://raw.githubusercontent.com/phuselab/pyVHR/master/pyVHR_env.yml

    #最后再安装pip install pyvhr
    
- 參考文獻 
    - 

