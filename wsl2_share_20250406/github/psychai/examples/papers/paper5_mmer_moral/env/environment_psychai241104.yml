name: psychai241104
channels:
  - pytorch
  - nvidia
  - defaults
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
  - https://repo.anaconda.com/pkgs/msys2
dependencies:
  - blas=1.0=mkl
  - brotli-python=1.0.9=py312hd77b12b_8
  - bzip2=1.0.8=h2bbff1b_6
  - ca-certificates=2024.9.24=haa95532_0
  - certifi=2024.8.30=py312haa95532_0
  - charset-normalizer=3.3.2=pyhd3eb1b0_0
  - cuda-cccl=12.6.77=0
  - cuda-cccl_win-64=12.6.77=0
  - cuda-cudart=12.1.105=0
  - cuda-cudart-dev=12.1.105=0
  - cuda-cupti=12.1.105=0
  - cuda-libraries=12.1.0=0
  - cuda-libraries-dev=12.1.0=0
  - cuda-nvrtc=12.1.105=0
  - cuda-nvrtc-dev=12.1.105=0
  - cuda-nvtx=12.1.105=0
  - cuda-opencl=12.6.77=0
  - cuda-opencl-dev=12.6.77=0
  - cuda-profiler-api=12.6.77=0
  - cuda-runtime=12.1.0=0
  - cuda-version=12.6=3
  - expat=2.6.3=h5da7b33_0
  - filelock=3.13.1=py312haa95532_0
  - freetype=2.12.1=ha860e81_0
  - giflib=5.2.2=h7edc060_0
  - idna=3.7=py312haa95532_0
  - intel-openmp=2023.1.0=h59b6b97_46320
  - jinja2=3.1.4=py312haa95532_0
  - jpeg=9e=h827c3e9_3
  - lcms2=2.12=h83e58a3_0
  - lerc=3.0=hd77b12b_0
  - libcublas=*********=0
  - libcublas-dev=*********=0
  - libcufft=********=0
  - libcufft-dev=********=0
  - libcurand=*********=0
  - libcurand-dev=*********=0
  - libcusolver=11.4.4.55=0
  - libcusolver-dev=11.4.4.55=0
  - libcusparse=12.0.2.55=0
  - libcusparse-dev=12.0.2.55=0
  - libdeflate=1.17=h2bbff1b_1
  - libffi=3.4.4=hd77b12b_1
  - libjpeg-turbo=2.0.0=h196d8e1_0
  - libnpp=12.0.2.50=0
  - libnpp-dev=12.0.2.50=0
  - libnvjitlink=12.1.105=0
  - libnvjitlink-dev=12.1.105=0
  - libnvjpeg=12.1.1.14=0
  - libnvjpeg-dev=12.1.1.14=0
  - libpng=1.6.39=h8cc25b3_0
  - libtiff=4.5.1=hd77b12b_0
  - libuv=1.48.0=h827c3e9_0
  - libwebp=1.3.2=hbc33d0d_0
  - libwebp-base=1.3.2=h3d04722_1
  - lz4-c=1.9.4=h2bbff1b_1
  - markupsafe=2.1.3=py312h2bbff1b_0
  - mkl=2023.1.0=h6b88ed4_46358
  - mkl-service=2.4.0=py312h2bbff1b_1
  - mkl_fft=1.3.10=py312h827c3e9_0
  - mkl_random=1.2.7=py312h0158946_0
  - mpmath=1.3.0=py312haa95532_0
  - networkx=3.2.1=py312haa95532_0
  - openjpeg=2.5.2=hae555c5_0
  - openssl=3.0.15=h827c3e9_0
  - pillow=10.4.0=py312h827c3e9_0
  - pip=24.2=py312haa95532_0
  - pysocks=1.7.1=py312haa95532_0
  - python=3.12.1=h1d929f7_0
  - pytorch=2.5.1=py3.12_cuda12.1_cudnn9_0
  - pytorch-cuda=12.1=hde6ce7c_6
  - pytorch-mutex=1.0=cuda
  - pyyaml=6.0.2=py312h827c3e9_0
  - requests=2.32.3=py312haa95532_0
  - setuptools=75.1.0=py312haa95532_0
  - sqlite=3.45.3=h2bbff1b_0
  - tbb=2021.8.0=h59b6b97_0
  - tk=8.6.14=h0416ee5_0
  - typing_extensions=4.11.0=py312haa95532_0
  - urllib3=2.2.3=py312haa95532_0
  - vc=14.40=h2eaa2aa_1
  - vs2015_runtime=14.40.33807=h98bb1dd_1
  - wheel=0.44.0=py312haa95532_0
  - win_inet_pton=1.1.0=py312haa95532_0
  - xz=5.4.6=h8cc25b3_1
  - yaml=0.2.5=he774522_0
  - zlib=1.2.13=h8cc25b3_1
  - zstd=1.5.6=h8880b57_0
  - pip:
      - absl-py==2.1.0
      - accelerate==1.1.0
      - aiohappyeyeballs==2.4.3
      - aiohttp==3.10.10
      - aiosignal==1.3.1
      - albucore==0.0.20
      - albumentations==1.4.21
      - annotated-types==0.7.0
      - asttokens==2.4.1
      - attrs==24.2.0
      - audeer==2.2.0
      - audformat==1.3.1
      - audinterface==1.2.2
      - audiofile==1.5.0
      - audioread==3.0.1
      - audmath==1.4.1
      - audobject==0.7.11
      - audresample==1.3.3
      - bitsandbytes==0.44.1
      - cloudpickle==3.1.0
      - colorama==0.4.6
      - comm==0.2.2
      - contourpy==1.3.0
      - cycler==0.12.1
      - datasets==3.1.0
      - debugpy==1.8.7
      - decorator==5.1.1
      - dill==0.3.8
      - eval-type-backport==0.2.0
      - executing==2.1.0
      - flatbuffers==24.3.25
      - fonttools==4.54.1
      - frozenlist==1.5.0
      - fsspec==2024.9.0
      - huggingface-hub==0.26.2
      - importlib-metadata==8.5.0
      - ipykernel==6.29.5
      - ipython==8.29.0
      - ipywidgets==8.1.5
      - iso3166==2.1.1
      - iso639-lang==2.5.0
      - jedi==0.19.1
      - jieba==0.42.1
      - joblib==1.4.2
      - jupyter-client==8.6.3
      - jupyter-core==5.7.2
      - jupyterlab-widgets==3.0.13
      - kiwisolver==1.4.7
      - lazy-loader==0.4
      - librosa==0.10.2.post1
      - lightning-utilities==0.11.8
      - llvmlite==0.43.0
      - matplotlib==3.9.2
      - matplotlib-inline==0.1.7
      - msgpack==1.1.0
      - multidict==6.1.0
      - multiprocess==0.70.16
      - nest-asyncio==1.6.0
      - numba==0.60.0
      - numpy==1.26.4
      - opencv-python==*********
      - opencv-python-headless==*********
      - opensmile==2.5.0
      - opt-einsum==3.4.0
      - oyaml==1.0
      - packaging==24.1
      - pandas==2.2.3
      - parso==0.8.4
      - peft==0.13.2
      - platformdirs==4.3.6
      - pooch==1.8.2
      - prompt-toolkit==3.0.48
      - propcache==0.2.0
      - protobuf==4.25.5
      - psutil==6.1.0
      - pure-eval==0.2.3
      - pyarrow==18.0.0
      - pycparser==2.22
      - pydantic==2.9.2
      - pydantic-core==2.23.4
      - pygments==2.18.0
      - pyparsing==3.2.0
      - python-dateutil==2.9.0.post0
      - pytorch-lightning==2.4.0
      - pytz==2024.2
      - pywin32==308
      - pyzmq==26.2.0
      - regex==2024.9.11
      - safetensors==0.4.5
      - scikit-learn==1.5.2
      - scipy==1.14.1
      - seaborn==0.13.2
      - sentencepiece==0.2.0
      - shap==0.46.0
      - simsimd==6.0.1
      - six==1.16.0
      - slicer==0.0.8
      - soundfile==0.12.1
      - soxr==0.5.0.post1
      - stack-data==0.6.3
      - stringzilla==3.10.7
      - sympy==1.13.1
      - threadpoolctl==3.5.0
      - tokenizers==0.20.1
      - torchaudio==2.5.1
      - torchmetrics==1.5.1
      - torchvision==0.20.1
      - tornado==6.4.1
      - tqdm==4.66.6
      - traitlets==5.14.3
      - transformers==4.46.1
      - tzdata==2024.2
      - wcwidth==0.2.13
      - widgetsnbextension==4.0.13
      - xxhash==3.5.0
      - yarl==1.17.1
      - zipp==3.20.2
prefix: C:\Users\<USER>\anaconda3\envs\psychai241104
