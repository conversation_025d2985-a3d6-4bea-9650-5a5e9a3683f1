import asyncio
import json
import os
import aiohttp
from bilibili_api import user
from bilibili_api import Credential

credential = Credential(sessdata="98d8dda7%2C1750127341%2C4e007%2Ac2CjBXKG9aQA1Z0s1OXRoAFNMhyw6FXVhpYYDKZX39lsUUENezJsw29sGT8WHf-i7uUOMSVjlPSmF2QnRtUkpXek5VZWlVOVl4S2N3RkdZWERuSmlGM3FhNGQ0dFVGcHl1a2h3N2x0WFZjMXdULURHU0JzOWdjVnhzbVRYNmZBVU9MV1FkTFRyRGNnIIEC",
                        bili_jct="ca131bed85f1076857f5e3fda73e9fb6",
                        buvid3="94D521DD-C5FC-27E5-231A-D060A9864ABD24380infoc",
                        dedeuserid="444882597",
                        ac_time_value="434863e93a8fc7c1032a7ad5d7e77dc2")

# Define a list of UIDs
UID = 444882597

# Default to no download
no_download = True

async def fetch(session: aiohttp.ClientSession, url: str, path: str):
    try:
        async with session.get(url) as resp:
            with open(path, 'wb') as fd:
                while True:
                    chunk = await resp.content.read(1024)  # 每次获取1024字节
                    if not chunk:
                        break
                    fd.write(chunk)
    except Exception as e:
        print(f"failed {url}: {e}")

def copyKeys(src, keys):
    res = {}
    for k in keys:
        if k in src:
            res[k] = src[k]
    return res

def getItem(input):
    if "item" in input:
        return getItem(input["item"])
    if "videos" in input:
        return getVideoItem(input)
    else:
        return getNormal(input)

def getNormal(input):
    res = copyKeys(input, ['description', 'pictures', 'content'])
    if "pictures" in res:
        res["pictures"] = [pic["img_src"] for pic in res["pictures"]]
    return res

def getVideoItem(input):
    res = copyKeys(input, ['title', 'desc', 'dynamic', 'short_link', 'stat', 'tname'])
    res["av"] = input["aid"]
    res["pictures"] = [input["pic"]]
    return res

def cardToObj(input):
    res = {
        "dynamic_id": input["desc"]["dynamic_id"],
        "timestamp": input["desc"]["timestamp"],
        "type": input["desc"]["type"],
        "item": getItem(input["card"])
    }
    if "origin" in input["card"] and input["card"]["origin"]:
        try:
            originObj = json.loads(input["card"]["origin"])
            res["origin"] = getItem(originObj)
            if "user" in originObj and "name" in originObj["user"]:
                res["origin_user"] = originObj["user"]["name"]
        except json.JSONDecodeError as e:
            print(f"JSON解析错误：{e}")
            res["origin"] = {}  # 如果解析失败，设置一个空的origin对象
    return res

async def main():
    u = user.User(uid=UID, credential=credential)
    filename = f"{UID}.json"
    with open(filename, "w", encoding="UTF-8") as f:
        f.write("[")  # 开始JSON数组
        offset = 0
        count = 0
        if not no_download:
            os.makedirs("pics", exist_ok=True)
        while True:
            res = await u.get_dynamics(offset)
            if res["has_more"] != 1:
                break
            offset = res["next_offset"]
            for card in res["cards"]:
                cardObj = cardToObj(card)
                if not no_download:
                    tasks = []
                    async with aiohttp.ClientSession() as session:
                        if "pictures" in cardObj["item"]:
                            for pic_url in cardObj["item"]["pictures"]:
                                task = fetch(session, pic_url, os.path.join("pics", os.path.basename(pic_url)))
                                tasks.append(task)
                        await asyncio.gather(*tasks)

                # 只输出content和origin的内容
                output = {"content": cardObj["item"].get("content", "")}
                if "origin" in cardObj:
                    origin_content = {
                        "title": cardObj["origin"].get("title", ""),
                        "desc": cardObj["origin"].get("desc", ""),
                        "dynamic": cardObj["origin"].get("dynamic", ""),
                        "stat": cardObj["origin"].get("stat", {}),
                        "tname": cardObj["origin"].get("tname", ""),
                        "av": cardObj["origin"].get("av", ""),
                        "pictures": cardObj["origin"].get("pictures", [])
                    }
                    output["origin"] = origin_content

                # 打印和保存内容
                if count > 0:
                    f.write(",\n")  # JSON数组中的逗号分隔符
                f.write(json.dumps(output, ensure_ascii=False))
                print(json.dumps(output, ensure_ascii=False))
                count += 1
            await asyncio.sleep(1)
        f.write("\n]")  # 结束JSON数组
        print(f"--------已完成！用户 {UID} 的动态已保存为 {filename} ---------")

if __name__ == '__main__':
    asyncio.get_event_loop().run_until_complete(main())