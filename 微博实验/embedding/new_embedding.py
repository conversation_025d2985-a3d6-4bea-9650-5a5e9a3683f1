import torch
from transformers import AutoTokenizer, AutoModel
import numpy as np


# 加载本地BERT模型和分词器
model_path = "D:\YLD\Scientific_Research_L\情感计算\文本分析预测\模型\\bert-base-chinese"
tokenizer = AutoTokenizer.from_pretrained(model_path)
model = AutoModel.from_pretrained(model_path)

# 定义量表选项
scale_options = {
    "Q9_1": "不认同自己容易感到焦虑",
    "Q9_2": "偶尔认同自己容易感到焦虑",
    "Q9_3": "一般认同自己容易感到焦虑",
    "Q9_4": "有时候自己容易感到焦虑",
    "Q9_5": "完全认同自己容易感到焦虑",
}


def get_embedding(text):
    """
    获取文本的词向量表示
    :param text: 输入的文本
    :return: 文本的词向量
    """
    inputs = tokenizer(text, return_tensors='pt', truncation=True, padding='max_length', max_length=512)
    with torch.no_grad():
        outputs = model(**inputs)
    # 这里使用[CLS]标记的输出作为文本的表示
    embedding = outputs.last_hidden_state[:, 0, :].squeeze().numpy()
    return embedding


def calculate_similarity(vec1, vec2):
    """
    计算两个向量的余弦相似度
    :param vec1: 向量1
    :param vec2: 向量2
    :return: 余弦相似度
    """
    dot_product = np.dot(vec1, vec2)
    norm_vec1 = np.linalg.norm(vec1)
    norm_vec2 = np.linalg.norm(vec2)
    similarity = dot_product / (norm_vec1 * norm_vec2)
    return similarity


def predict_scale_option(passage):
    """
    根据输入的篇章文本预测最匹配的量表选项
    :param passage: 输入的篇章文本
    :return: 最匹配的量表选项
    """
    passage_embedding = get_embedding(passage)
    similarities = []
    for option, option_text in scale_options.items():
        option_embedding = get_embedding(option_text)
        similarity = calculate_similarity(passage_embedding, option_embedding)
        similarities.append((option, similarity))
    # 找到相似度最高的选项
    best_option = max(similarities, key=lambda x: x[1])[0]
    return best_option


# 示例使用
passage = "这是一个示例篇章，用于测试量表选项预测。"
predicted_option = predict_scale_option(passage)
print(f"预测的量表选项是: {predicted_option}")
