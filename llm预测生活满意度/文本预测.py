# -*- coding: utf-8 -*-
"""
数字表型心理测量 - SWLS生活满意度预测模型
基于大语言模型分析个人文本回答，预测其在SWLS量表上的得分
Please install OpenAI SDK first: `pip3 install openai`
"""

import json
import os
import time
import threading
from openai import OpenAI
from typing import List, Dict, Any

class SWLSPredictor:
    """SWLS生活满意度预测器"""

    def __init__(self, api_key: str = "sk-75935f13ca624fc4a87471613b6bb0b6"):
        """初始化预测器"""
        self.client = OpenAI(
            api_key=api_key,
            base_url="https://api.deepseek.com"
        )
        self.swls_questions = self._load_swls_questions()

    def _load_swls_questions(self) -> List[Dict]:
        """加载SWLS题目和选项"""
        try:
            # 使用绝对路径
            file_path = r"C:\Users\<USER>\Desktop\项目实验代码\llm预测生活满意度\SWLS题目选项得分.py"

            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 去掉Python文件的变量赋值部分，只保留JSON数据
                json_start = content.find('[')
                json_content = content[json_start:]
                questions = json.loads(json_content)
                print(f"✅ 成功加载 {len(questions)} 个SWLS题目")
                return questions

        except Exception as e:
            print(f"❌ 加载SWLS题目失败: {e}")
            return []

    def _load_participant_data(self, filename: str) -> List[Dict]:
        """加载被试数据"""
        try:
            # 构建绝对路径
            base_path = r"C:\Users\<USER>\Desktop\项目实验代码\llm预测生活满意度"
            file_path = os.path.join(base_path, filename)

            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 去掉Python文件的变量赋值部分，只保留JSON数据
                json_start = content.find('[')
                json_content = content[json_start:]
                data = json.loads(json_content)
                print(f"✅ 成功加载 {len(data)} 条被试问答记录")
                return data

        except Exception as e:
            print(f"❌ 加载被试数据失败: {e}")
            print(f"尝试的文件路径: {os.path.join(base_path, filename) if 'base_path' in locals() else filename}")
            return []

    def _show_progress(self, stop_event):
        """显示大模型工作进度"""
        progress_chars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
        i = 0
        while not stop_event.is_set():
            print(f"\r🤖 大模型正在分析中 {progress_chars[i % len(progress_chars)]} 请稍候...", end='', flush=True)
            time.sleep(0.2)
            i += 1
        print("\r" + " " * 50 + "\r", end='', flush=True)  # 清除进度显示

    def _create_analysis_prompt(self, participant_data: List[Dict]) -> str:
        """创建分析提示词"""
        # 整合所有问答内容
        all_responses = ""
        participant_id = ""
        for item in participant_data:
            if not participant_id:
                participant_id = item.get('被试编号', 'unknown')
            all_responses += f"问题：{item['问题']}\n"
            all_responses += f"回答：{item['答案']}\n\n"

        # 构建SWLS题目信息
        swls_info = ""
        for i, question in enumerate(self.swls_questions, 1):
            swls_info += f"{i}. {question['item_text']}\n"
            swls_info += "选项：\n"
            for option in question['options']:
                swls_info += f"  {option['score']}分 - {option['text']}\n"
            swls_info += "\n"

        prompt = f"""
你是一位专业的心理学家，擅长通过文本分析来评估个体的心理状态。现在需要你基于一个被试的详细文本回答，预测其在SWLS生活满意度量表上的得分。

## 被试编号：{participant_id}

## 被试的文本回答：
{all_responses}

## SWLS生活满意度量表题目和选项：
{swls_info}

## 分析任务：
请仔细分析被试的所有文本回答，从中提取关于其生活满意度、幸福感、人生态度等相关信息，然后预测该被试在SWLS量表的5个题目上分别会选择哪个选项（1-7分）。

## 分析要点：
1. 关注被试对生活现状的满意程度
2. 分析其对理想生活的描述和现实的差距
3. 观察其对人生重要事物的获得感
4. 评估其对过往选择的满意度
5. 综合判断其整体生活满意度水平

## 输出格式：
请严格按照以下JSON格式输出预测结果：

```json
{{
  "participant_id": "{participant_id}",
  "analysis_summary": "基于文本的整体分析总结（200字以内）",
  "predictions": [
    {{
      "question_id": "SWLS_1",
      "question_text": "我的生活中大多数方面都接近理想状态",
      "predicted_score": 预测分数(1-7),
      "reasoning": "预测理由（100字以内）"
    }},
    {{
      "question_id": "SWLS_2",
      "question_text": "我的生活状况非常棒",
      "predicted_score": 预测分数(1-7),
      "reasoning": "预测理由（100字以内）"
    }},
    {{
      "question_id": "SWLS_3",
      "question_text": "我对我的生活感到满意",
      "predicted_score": 预测分数(1-7),
      "reasoning": "预测理由（100字以内）"
    }},
    {{
      "question_id": "SWLS_4",
      "question_text": "目前为止，我已经得到此生想要的重要的东西",
      "predicted_score": 预测分数(1-7),
      "reasoning": "预测理由（100字以内）"
    }},
    {{
      "question_id": "SWLS_5",
      "question_text": "如果还能重新再活一次，我不会对现有的生活做任何改变",
      "predicted_score": 预测分数(1-7),
      "reasoning": "预测理由（100字以内）"
    }}
  ],
  "total_score": 总分(5-35),
  "satisfaction_level": "生活满意度等级"
}}
```

请开始分析并给出预测结果：
"""
        return prompt
    def predict_swls_scores(self, participant_file: str) -> Dict[str, Any]:
        """预测SWLS得分"""
        print(f"开始分析被试文件: {participant_file}")

        # 加载被试数据
        participant_data = self._load_participant_data(participant_file)
        if not participant_data:
            return {"error": "无法加载被试数据"}

        print(f"成功加载 {len(participant_data)} 条问答记录")

        # 创建分析提示词
        prompt = self._create_analysis_prompt(participant_data)

        # 尝试API调用，如果失败则使用备选方案
        for attempt in range(3):
            try:
                print(f"🔄 第 {attempt + 1} 次尝试调用大模型...")

                # 启动进度显示
                stop_event = threading.Event()
                progress_thread = threading.Thread(target=self._show_progress, args=(stop_event,))
                progress_thread.start()

                try:
                    response = self.client.chat.completions.create(
                        model="deepseek-chat",
                        messages=[
                            {"role": "system", "content": "你是一位专业的心理学家，擅长通过文本分析来评估个体的心理状态和生活满意度。"},
                            {"role": "user", "content": prompt}
                        ],
                        stream=False,
                        temperature=0.3,  # 降低随机性，提高一致性
                        timeout=120  # 设置2分钟超时
                    )

                    # 停止进度显示
                    stop_event.set()
                    progress_thread.join()

                    result_text = response.choices[0].message.content
                    print("✅ 大模型分析完成！")

                    # 尝试解析JSON结果
                    try:
                        # 提取JSON部分
                        json_start = result_text.find('{')
                        json_end = result_text.rfind('}') + 1
                        if json_start != -1 and json_end != -1:
                            json_text = result_text[json_start:json_end]
                            result = json.loads(json_text)
                            return result
                        else:
                            print("⚠️ 无法从响应中提取JSON格式，使用备选方案...")
                            return self._create_fallback_prediction(participant_data, result_text)
                    except json.JSONDecodeError as e:
                        print(f"⚠️ JSON解析失败: {e}，使用备选方案...")
                        return self._create_fallback_prediction(participant_data, result_text)

                finally:
                    # 确保停止进度显示
                    stop_event.set()
                    if progress_thread.is_alive():
                        progress_thread.join()

            except Exception as api_error:
                # 停止进度显示
                stop_event.set()
                if progress_thread.is_alive():
                    progress_thread.join()

                print(f"❌ API调用失败 (尝试 {attempt + 1}/3): {api_error}")

                if attempt < 2:  # 如果不是最后一次尝试
                    print("⏳ 等待5秒后重试...")
                    time.sleep(5)
                else:
                    print("🔧 API调用多次失败，使用基于规则的预测方法...")
                    return self._create_rule_based_prediction(participant_data)

        # 如果所有尝试都失败了
        print("🔧 使用基于规则的预测方法...")
        return self._create_rule_based_prediction(participant_data)

    def _create_fallback_prediction(self, participant_data: List[Dict], raw_response: str = "") -> Dict[str, Any]:
        """创建备选预测结果（当API返回格式不正确时）"""
        participant_id = participant_data[0].get('被试编号', 'unknown') if participant_data else 'unknown'

        # 基于文本内容的简单分析
        all_text = " ".join([item.get('答案', '') for item in participant_data])

        # 简单的情感分析
        positive_words = ['满意', '开心', '快乐', '幸福', '好', '棒', '喜欢', '愉快', '顺利', '成功']
        negative_words = ['不满', '难过', '痛苦', '失败', '糟糕', '困难', '遗憾', '后悔', '焦虑', '压力']

        positive_count = sum(1 for word in positive_words if word in all_text)
        negative_count = sum(1 for word in negative_words if word in all_text)

        # 基于词汇分析的基础得分
        if positive_count > negative_count * 1.5:
            base_score = 5  # 偏积极
        elif negative_count > positive_count * 1.5:
            base_score = 3  # 偏消极
        else:
            base_score = 4  # 中性

        predictions = []
        for i, question in enumerate(self.swls_questions, 1):
            # 为每个题目添加一些随机变化
            score = max(1, min(7, base_score + (i % 3 - 1)))
            predictions.append({
                "question_id": f"SWLS_{i}",
                "question_text": question['item_text'],
                "predicted_score": score,
                "reasoning": f"基于文本分析的预测得分（备选方案）"
            })

        total_score = sum(pred['predicted_score'] for pred in predictions)

        return {
            "participant_id": participant_id,
            "analysis_summary": f"由于API响应格式问题，使用基于关键词分析的备选预测方法。检测到积极词汇{positive_count}个，消极词汇{negative_count}个。",
            "predictions": predictions,
            "total_score": total_score,
            "satisfaction_level": self.get_satisfaction_level(total_score),
            "method": "fallback_prediction",
            "raw_response": raw_response[:500] if raw_response else ""
        }

    def _create_rule_based_prediction(self, participant_data: List[Dict]) -> Dict[str, Any]:
        """创建基于规则的预测结果（当API完全失败时）"""
        participant_id = participant_data[0].get('被试编号', 'unknown') if participant_data else 'unknown'

        # 更详细的文本分析
        all_text = " ".join([item.get('答案', '') for item in participant_data])

        # 生活满意度相关关键词
        satisfaction_indicators = {
            'high': ['非常满意', '很满意', '特别开心', '非常幸福', '很棒', '完美', '理想', '成功', '顺利'],
            'medium_high': ['满意', '开心', '快乐', '幸福', '好', '不错', '还可以'],
            'medium': ['一般', '普通', '还行', '凑合', '中等'],
            'medium_low': ['不太满意', '有点失望', '一般般', '不够好'],
            'low': ['不满意', '失望', '糟糕', '痛苦', '困难', '后悔', '遗憾']
        }

        # 计算各类词汇出现次数
        scores = {'high': 0, 'medium_high': 0, 'medium': 0, 'medium_low': 0, 'low': 0}
        for level, words in satisfaction_indicators.items():
            scores[level] = sum(1 for word in words if word in all_text)

        # 根据词汇分布确定基础满意度水平
        if scores['high'] > 0 or scores['medium_high'] > scores['medium_low'] + scores['low']:
            base_scores = [5, 6, 5, 5, 4]  # 较高满意度
        elif scores['low'] > 0 or scores['medium_low'] > scores['medium_high']:
            base_scores = [3, 3, 4, 3, 2]  # 较低满意度
        else:
            base_scores = [4, 4, 4, 4, 4]  # 中等满意度

        predictions = []
        for i, (question, score) in enumerate(zip(self.swls_questions, base_scores), 1):
            predictions.append({
                "question_id": f"SWLS_{i}",
                "question_text": question['item_text'],
                "predicted_score": score,
                "reasoning": f"基于关键词分析的规则预测（高:{scores['high']}, 中高:{scores['medium_high']}, 中:{scores['medium']}, 中低:{scores['medium_low']}, 低:{scores['low']}）"
            })

        total_score = sum(pred['predicted_score'] for pred in predictions)

        return {
            "participant_id": participant_id,
            "analysis_summary": f"使用基于规则的文本分析方法。文本长度: {len(all_text)}字符。关键词分布 - 高满意度词汇: {scores['high']}个, 中高: {scores['medium_high']}个, 中等: {scores['medium']}个, 中低: {scores['medium_low']}个, 低: {scores['low']}个。",
            "predictions": predictions,
            "total_score": total_score,
            "satisfaction_level": self.get_satisfaction_level(total_score),
            "method": "rule_based_prediction"
        }

    def get_satisfaction_level(self, total_score: int) -> str:
        """根据总分获取满意度等级"""
        if 31 <= total_score <= 35:
            return "非常满意"
        elif 26 <= total_score <= 30:
            return "满意"
        elif 21 <= total_score <= 25:
            return "稍微满意"
        elif total_score == 20:
            return "中立"
        elif 15 <= total_score <= 19:
            return "稍微不满意"
        elif 10 <= total_score <= 14:
            return "不满意"
        elif 5 <= total_score <= 9:
            return "非常不满意"
        else:
            return "分数超出范围"


    def _get_option_text(self, score: int) -> str:
        """根据分数获取选项文本"""
        option_mapping = {
            1: "非常不赞同",
            2: "不赞同",
            3: "有点不赞同",
            4: "不确定/中立",
            5: "有点赞同",
            6: "赞同",
            7: "非常赞同"
        }
        return f"{score}分 - {option_mapping.get(score, '未知选项')}"
    def print_results(self, results: Dict[str, Any]):
        """打印预测结果"""
        if "error" in results:
            print(f"❌ 错误: {results['error']}")
            if "raw_response" in results:
                print(f"原始响应: {results['raw_response']}")
            return

        print("\n" + "="*60)
        print("🧠 SWLS生活满意度预测结果")
        print("="*60)

        print(f"📋 被试编号: {results.get('participant_id', 'unknown')}")
        print(f"📝 分析总结: {results.get('analysis_summary', '无')}")

        print("\n📊 各题目预测得分:")
        print("-" * 50)

        total_predicted = 0
        for i, pred in enumerate(results.get('predictions', []), 1):
            score = pred.get('predicted_score', 0)
            total_predicted += score

            print(f"{i}. {pred.get('question_text', '')}")
            print(f"   📝 预测得分: {score}/7")

            # 显示对应的选项文本
            if score >= 1 and score <= 7:
                option_text = self._get_option_text(score)
                print(f"   🎯 选择选项: {option_text}")

            print(f"   💭 预测理由: {pred.get('reasoning', '')}")
            print()

        print(f"🎯 总分: {total_predicted}/35")
        print(f"📈 生活满意度等级: {self.get_satisfaction_level(total_predicted)}")
        print("="*60)


def main():
    """主程序"""
    print("🚀 启动SWLS生活满意度预测系统")
    print("基于数字表型心理测量技术")
    print("-" * 50)

    # 初始化预测器
    try:
        predictor = SWLSPredictor()
        print("✅ 预测器初始化成功")
    except Exception as e:
        print(f"❌ 预测器初始化失败: {e}")
        return

    # 定义要预测的被试文件列表
    participant_files = [
        "<EMAIL>",
        "<EMAIL>"
    ]

    all_results = {}

    # 对每个被试进行预测
    for i, participant_file in enumerate(participant_files, 1):
        print(f"\n{'='*60}")
        print(f"🎯 开始预测被试 {i}/{len(participant_files)}: {participant_file}")
        print(f"{'='*60}")

        results = predictor.predict_swls_scores(participant_file)
        predictor.print_results(results)

        # 保存单个结果
        try:
            output_file = f"SWLS预测结果_{participant_file.replace('.py', '.json')}"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"\n💾 结果已保存到: {output_file}")

            # 收集到总结果中
            participant_id = participant_file.replace('.py', '')
            all_results[participant_id] = results

        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

    # 生成对比总结
    print(f"\n{'='*80}")
    print("📊 多被试SWLS预测结果对比总结")
    print(f"{'='*80}")

    for participant_id, results in all_results.items():
        if "error" not in results:
            total_score = results.get('total_score', 0)
            satisfaction_level = results.get('satisfaction_level', '未知')
            print(f"👤 {participant_id}: {total_score}/35分 - {satisfaction_level}")

    # 保存汇总结果
    try:
        summary_file = "SWLS预测结果_汇总.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        print(f"\n💾 汇总结果已保存到: {summary_file}")
    except Exception as e:
        print(f"❌ 保存汇总结果失败: {e}")

    print(f"\n🎉 所有预测完成！共分析了 {len(participant_files)} 个被试")


if __name__ == "__main__":
    main()