import pandas as pd
from comprehensive_function import gpt_call


def process_excel(excel_file):

    df = pd.read_excel(excel_file)

    # 指定所需列
    user_column = 'individual'
    j = 1

    # 外层行，内层列
    for index, row in df.iterrows():
        from user_elements import topic, random
        text = row['condensed_descriptions']
        user_name = df.at[index, user_column]
        print(f'User {j} : {user_name}\n====================================================================================================')
        man_made_text = []
        for i in range(100):
            print(f"DAY {i+1}: \n----------------------------------------------------------------------------------------------------")
            how_many_sentences_today = random.randint(1, 4)
            print(f"how_many_sentences_today: {how_many_sentences_today}")

            for k in range(how_many_sentences_today):
                index_of_topic = random.randint(0, len(topic)-1)
                print(f"index_of_topic: {index_of_topic}")
                print(f"topic_now: {topic[index_of_topic]}")

                prompt_API_dataset = '''Let's think step by step. 
                            1.Task Background: As a professional psychology researcher, I need you to generate what a user is likely to write on social media
                            in a day to express his feelings in 20 words, based on his self-statement, the required text length, the topic of the text, I'll 
                            tell you about these elements below. You have to focus on step 2 and 4 thus you'll know clearly about all of details you want. 
                            2.Self-statement:''' + str(text) + '''
                            Other requirements: Each sentence should be about 15 words long
                            3.Topic of the current generated text:''' + str(topic[index_of_topic]) + '''
                            4.Everything this user has written so far on the platform:''' + str(' '.join(man_made_text)) + '''. It's gorgeous to mention these content.
                            5.Generation format: "Manchester City and Manchester United always make the blood boil, but tonight's defeat has made my heart even heavier."
                            6.JUST GENERATE ABOUT 15 WORDS!'''

                response = gpt_call(prompt_API_dataset)
                print(f"GPT's response: {response}\n")
                man_made_text.append(response)

        # 填充结果到 DataFrame 中
        print(man_made_text)
        df.at[index, 'man_made_text'] = ' '.join(man_made_text)
        j += 1

        # 将修改后的 DataFrame 写回到原始 Excel 文件的同一工作表中
        df.to_excel(excel_file, index=False, engine='openpyxl')

        # if j == 2:
        #     break

excel_file = "real_2020T2_training_data.xlsx"
process_excel(excel_file)