import pandas as pd
import random
from comprehensive_function import gpt_call
from materials import *
from user_elements import *

# random.seed控制random生成的随机数可复现，如果改为np.random.seed将不可复现，因为from materials import *已经引用了no.random.seed，而该脚本的random没设置种子
random.seed(7)

def summarize_9_descriptions(excel_file):
    df = pd.read_excel(excel_file)
    j = 1
    # 指定所需列
    user_column = 'individual'

    # 同步遍历 Excel 的一行和字典
    for index, row in df.iterrows():
        user_name = df.at[index, user_column]
        print(f'User {j} : {user_name}\n====================================================================================================')
        summary_for_9 = []
        for category, details in materials.items():

            prompt_9_score_synthesis = '''Let's think step by step.
                            1.Task description:Here is a dictionary of the user's responses to a certain psychological indicator scale, which requires you to generate a 50-word summary assessment of
                            the user based on 'questionnaire introduction', 'questions and options',' result analysis', and 'real score'
                            2.Task goal:We're doing supervised learning, thus your summary assessment MUST allow LLM to determine the user's score on the psychological indicators of the questionnaire.
                            3.Questionnaire and result:''' + str(details) + '''
                            4.JUST GENERATE ABOUT 50 WORDS!'''

            response = gpt_call(prompt_9_score_synthesis)
            print(f"GPT's response: {response}\n")

            # 将响应写入当前行的相应列
            # df.at[index, category] = response
            summary_for_9.append(response)

        # 一个用户循环结束将9份合并
        df.at[index, 'summary_for_9'] = ' '.join(summary_for_9)

    # 将修改后的 DataFrame 写回到原始 Excel 文件的同一工作表中
    df.to_excel(excel_file, index=False, engine='openpyxl')

def mmd_text(excel_file):

    df = pd.read_excel(excel_file)

    # 指定所需列
    user_column = 'individual'
    j = 1

    # 外层行，内层列
    for index, row in df.iterrows():

        text = row['summary_for_9']
        # 个人信息三变量
        gender = random.choice(["male", "female"])
        age = random.randint(10, 70)
        interests = random.choices(topic, k=3)

        user_name = df.at[index, user_column]
        print(f'User {j} : {user_name}\n====================================================================================================')
        print(f"Gender: {gender}")
        print(f"Age: {age}")
        print(f"Interests: {interests}")

        man_made_text = []
        for i in range(1):
            print(f"DAY {i+1}: \n----------------------------------------------------------------------------------------------------")
            how_many_sentences_today = random.randint(1, 4)
            print(f"how_many_sentences_today: {how_many_sentences_today}")

            for k in range(how_many_sentences_today):
                index_of_topic = random.randint(0, len(topic)-1)

                prompt_9_score_synthesis = {

                            "1.Task Background": '''As a professional psychology researcher, I need you to generate what a user is likely to write on social media
                            in a day to express his feelings in 50 words, based on his SELF-STATEMENT(), the required text length, the topic of the text, I'll 
                            tell you about these elements below. This prompt includes the user's information and some rules.''',
                            "2.Self-statement": row['summary_for_9'],
                            "Other requirements": "Each sentence should be about 50 words long",
                            "3.Topic of the current generated text": str(topic[index_of_topic]),
                            "4.Everything this user has written so far on the platform":str(' '.join(man_made_text)),
                            "5.Gender":gender,
                            "6.Age:":age,
                            "7.Interests:":interests,
                            "8.Emphasis:":"Your simulated social media message has a strong personal characteristic of the user, and it is obvious that the message is "
                                          "customized for him.",
                            "9.Psychological scale:":materials,
                            }
                print(f"index_of_topic: {index_of_topic}")
                print(f"topic_now: {topic[index_of_topic]}")

                response = gpt_call(str(prompt_9_score_synthesis))
                print(f"GPT's response: {response}\n")
                man_made_text.append(response)

        # 填充结果到 DataFrame 中
        # print(man_made_text)
        df.at[index, 'man_made_text'] = ' '.join(man_made_text)
        j += 1

        # 将修改后的 DataFrame 写回到原始 Excel 文件的同一工作表中
        df.to_excel(excel_file, index=False, engine='openpyxl')

        # if j == 2:
        #     break

excel_file = "9_score_description.xlsx"

# 第一步是根据量表生成评估
# summarize_9_descriptions(excel_file)

# 第二步是生成社媒留言
mmd_text(excel_file)