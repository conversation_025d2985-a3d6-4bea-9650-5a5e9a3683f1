import pandas as pd
from CLEF_eRisk import eRisk
from comprehensive_function import gpt_call

topic = [
    "work from home",
    "Healthy diet",
    "Pet daily",
    "Fashion wear",
    "Travel guide",
    "Fitness experience",
    "Financial investment",
    "Reading notes",
    "Environmental life",
    "Sports event",
    "Psychological adjustment",
    "Gourmet scout",
    "The feeling of the movie",
    "Parent-child education",
    "Beauty and skin care",
    "Photographic skill",
    "handicraft",
    "Game experience",
    "Festival celebration",
    "Career guide",
    "Music sharing",
    "celebrities"
]

interests = [
    "Work from Home",
    "Healthy Diet",
    "Pet Daily",
    "Fashion Wear",
    "Travel Guide",
    "Fitness Experience",
    "Financial Investment",
    "Reading Notes",
    "Environmental Life",
    "Sports Event",
    "Psychological Adjustment",
    "Gourmet Scout",
    "The Feeling of the Movie",
    "Parent-Child Education",
    "Beauty and Skin Care",
    "Photographic Skill",
    "Handicraft",
    "Game Experience",
    "Festival Celebration",
    "Career Guide",
    "Music Sharing",
    "Celebrities"
]

def find_descriptions(ratings):
    descriptions = []
    # 遍历 ratings 中的每个评级
    for i, rating in enumerate(ratings.split()):
        item = eRisk[i]
        for desc, value in item.items():
            if value == rating:
                clean_desc = desc.split(":")[0]  # 只取冒号前的部分作为描述
                descriptions.append(clean_desc)
                break

    return descriptions

# # 随机从社交媒体热门话题集 中选择当前生成文本的话题
# index_of_topic = random.randint(0, len(topic))
# print(f"index_of_topic: {index_of_topic}")
#
# # 每位用户每天发表的条数：最少1条，最多3条
# num_of_day = random.randint(1,4)
# print(f"num_of_day: {num_of_day}")


# 将真实做答结果的选项整合到一起作为对用户的描述
excel_file_path = 'real_2020T2_training_data.xlsx'
df = pd.read_excel(excel_file_path, engine='openpyxl')

# 假设数字位于连续的列中，例如从'Column2'到'Column22'
# 遍历DataFrame的每一行
if __name__ == "__main__":
    for index, row in df.iterrows():
        # 提取一行中的20个数字，假设它们存储为字符串，使用空格连接
        # ratings = ' '.join(row['Column2':'Column22'].astype(str))
        # print(f"Row {index + 1}: {ratings}")
        #
        # # 调用之前定义的函数来找到描述
        # descriptions = find_descriptions(ratings)
        # df.at[index, 'descriptions'] = ' '.join(descriptions)
        # print(len(descriptions))

        text = df['descriptions']
        prompt_user_elements = "Compress the text to about 50 words, but do not change the original meaning. text: " + str(text)
        condense = gpt_call(prompt_user_elements)
        print(f"\ncondensed_descriptions: {str(condense)}")
        df.at[index, 'condensed_descriptions'] = condense

    # 将最终的DataFrame写回到Excel文件
    df.to_excel(excel_file_path, index=False, engine='openpyxl')


