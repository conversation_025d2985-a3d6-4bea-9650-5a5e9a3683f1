import pandas as pd
import re
from comprehensive_function import gpt_call
from materials import *


def process_excel(excel_file):
    df = pd.read_excel(excel_file)

    # 指定所需列
    user_column = 'individual'

    # 通过行索引号（从0开始）控制循环
    for index in range(1):

        # 选择索引对应的行
        row = df.iloc[index]
        # 预测文本为人工定制的数据
        text = row['man_made_text']
        user_name = row[user_column]
        print(f'User {index+1} : {user_name}\n====================================================================================================')

        # 提示词是主要调制手段
        # prompt_API_prediction = {
        #     "1.Task description:":"You are a professional psychological researcher who is assigned to predict a user's scores of 9 psychological scales"
        #                           "including Positive, Negative, Depression, Anxiety, Neuroticism, Extroversion, Openness, Friendliness and Stringency.",
        #     "2.Psychological scales:":materials,
        #     "3.User's social media message"
        # }

        response = gpt_call(prompt_API_prediction)
        print("GPT's response: \n", response)
        number = re.findall(r'\d+', response)

        result = number[-1]
        print(f"\nFinal prediction: {result}\n----------------------------------------------------------------------------------------------------")

        # 填充结果到 DataFrame 中
        df.at[index, 'total_score'] = result

    # 将修改后的 DataFrame 写回到原始 Excel 文件的同一工作表中
    df.to_excel(excel_file, index=False, engine='openpyxl')


excel_file = "9_score_description.xlsx"
process_excel(excel_file)
