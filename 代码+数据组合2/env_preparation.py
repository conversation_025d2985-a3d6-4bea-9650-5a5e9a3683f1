# 基础库和数据处理
import collections  # 提供了许多有用的集合类
import numpy as np  # 数学库，用于高效的多维数组操作
import pandas as pd  # 数据处理和分析库
import torch  # 深度学习库，提供了多维数组对象和大量操作这些数组的函数
import faiss


# 机器学习和自然语言处理
from gensim import corpora, models, similarities  # 用于自然语言处理的库，提供了模型和相似度计算等功能
from gensim.models import Word2Vec, KeyedVectors  # 词嵌入模型和预训练模型的加载
from sklearn.feature_extraction.text import CountVectorizer, TfidfTransformer, TfidfVectorizer  # 文本特征提取，包括计数向量化和TF-IDF变换
from sklearn.metrics.pairwise import cosine_similarity  # 用于计算余弦相似度
from transformers import AutoTokenizer, AutoModel, BertTokenizer, BertModel  # transformers库提供的预训练模型和分词器
from sentence_transformers import SentenceTransformer, util
from sklearn.decomposition import LatentDirichletAllocation
# 可视化
from wordcloud import WordCloud  # 词云生成库

# 其他
import jieba  # 导入jieba分词库
from scipy.spatial.distance import cosine  # 计算余弦距离

import gzip  # 用于读写gzip格式的文件
import gensim  # 自然语言处理库，用于加载和使用各种NLP模型
from dotenv import load_dotenv
import os
import collections
import re  # 导入正则表达式库

import numpy as np
from sklearn.manifold import TSNE
from sklearn.datasets import load_digits  # 仅作为示例数据
import plotly.graph_objects as go
from nltk.tokenize import sent_tokenize

device = "cuda:0" if torch.cuda.is_available() else "cpu"

load_dotenv()  # This loads the variables from .env
huggingface_cache_location = os.path.abspath("../../" + os.getenv("huggingface_cache_location"))
datasets_cache_location = os.path.abspath("../../" + os.getenv("datasets_cache_location"))
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

# mental_disorders = {
#     "I have a very low anxiety rate": "1",
#     "I don't get anxious very easily":"2",
#     "I'm neutral about how prone I am to anxiety":"3",
#     "I tend to get anxious":"4",
#     "I get anxious very easily":"5"
# }
#
#
# user_text = ("Always stay alive, even if they suffered from any tragedy."
#              "If my mistake is there then I will accept it but if not then i will tell them to behave properly with good wordings."
#              "All they have done for me is always great for me, because I know that parents always do well for us."
#              "Be frank and talk about current news, new technologies as you always share with your friend. Also make fun by do nice things."
#              "How can you say that if you have not experienced it?"
#              "When I was in 6th standard, one of my friend gave invitation to the group (in which I was also included) for his birthday celebration but didn't invite me. When I came to know about it from my another friend after his birthday celebration, I stopped talking to him."
#              )

test = ["I felt extreme sadness and despair, as if no matter what I did, I could not get rid of this emotion. I felt hope so far away that it was almost impossible to reach. I was overwhelmed with guilt and felt like I was burdening the world. I constantly blamed myself and felt that I had done many things wrong and did not deserve to be forgiven. I began to not recognize myself, it became painful to be around myself, and I regretted my actions and thoughts.",
"I was constantly criticizing myself for not being good enough, not being perfect enough. This self-criticism became a constant in my life, and I constantly reminded myself of my shortcomings and failures. Sometimes, I wanted to end the pain, and that thought stuck in my head, and it got stronger and stronger as time went on.",
"I no longer interact with the world the way I used to, and I feel disconnected from everything. I stopped socializing and caring about the things I once loved. It became very difficult for me to make decisions, and even the simplest choices made me hesitate. I felt worthless, insignificant in this world, like a speck of dust in the universe. I feel very tired every day, and this fatigue makes it difficult for me to cope with the challenges of daily life.",
"I don't sleep well, I lie in bed at night with all kinds of thoughts in my head that make it difficult for me to fall asleep. When I woke up in the morning, I felt even more tired and depressed. I became easily angry, and even the slightest touch or disturbance would cause me to explode. I no longer had an appetite, didn't want to eat, and even lost interest in the foods I used to love. ",
"I had a hard time concentrating, my mind was all over the place and I couldn('t focus on anything. I felt very tired, and this exhaustion made me not interested in anything, including sex. I felt myself trapped in an endless loop, unable to get out of this sad and exhausted state.I felt very sad, but I found that I could not cry, my emotions seemed to have been drained. I felt anxious and uneasy, and the tension was making me lose my mind, and I felt like I was hovering on the edge of sanity.",
'E-O-P']

# 停用词设定
stop_words = ['\n', 'or', 'are', 'they', 'i', 'some', 'by', '—', 'was','from','which',
            'even', 'the', 'to', 'a', 'an', 'and', 'of', 'in', 'on', 'for',
            'that', 'with', 'is', 'as', 'could', 'its', 'this', 'other', 'like'
            'an', 'have', 'more', 'at', 'don’t', 'can', 'only', 'most', 'um', 'uh']

# 定义一个函数来分割文本并创建元组列表
def create_segments(text, max_length=512):
    segments = []  # 存储最终的字符串列表
    current_segment = ""  # 初始化当前字符串为空

    # 将text拆分为单个字符串列表
    strings = text.split("~")

    for string in strings:
        # 检查加上新字符串后的长度是否超过最大长度
        if len(current_segment) + len(string) + (current_segment != "") * 1 > max_length:
            # 如果超过长度限制，将当前字符串添加到segments列表，并开始新的当前字符串
            segments.append(current_segment)
            current_segment = string  # 新的当前字符串是下一个字符串
        else:
            # 如果当前字符串为空，则直接添加字符串
            if current_segment == "":
                current_segment = string
            # 如果不为空，则在字符串前加空格后添加
            else:
                current_segment += " " + '"'+ string

    # 添加最后一个字符串到segments列表
    if current_segment:
        segments.append(current_segment)

    return segments

# 初始化user_text列表
user_text_2019T3 = []
user_text_2020T2 = []
user_text_2020T2_training_data = []


# 读取Excel文件:
# 2019T3.xlsx
# 2020T2.xlsx
# 2020T2_training_data.xlsx
excel_file = "2019T3.xlsx"
df = pd.read_excel(excel_file)

# 遍历DataFrame的formatted_text列
for index, row in df.iterrows():
    # 对每一行的formatted_text列应用create_text_segments函数
    segments = create_segments(row['formatted_text'])

    for ele in segments:
        user_text_2019T3.append(ele)
    user_text_2019T3.append("E-O-P")

print(user_text_2019T3)
# print(user_text_2020T2)
# print(user_text_2020T2_training_data)

