from openai import OpenAI
from dotenv import load_dotenv
import pandas as pd
import os
import re
from comprehensive_function import remove_unwanted_content

# 加载.env文件中的环境变量
load_dotenv('API_KEY.env')

# 创建一个OpenAI客户端实例
client = OpenAI(
    api_key=os.getenv("OPENAI_API_KEY"),
    base_url=os.getenv("OPENAI_BASE_URL")
)

# 正则表达式，用于匹配所有非英文字符
non_english_pattern = re.compile(r'[^a-zA-Z\s]')

def gpt4_call(prompt):
    try:
        completion = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}]
        )
        return completion.choices[0].message.content
    except Exception as e:
        return str(e)

def process_excel(excel_file):
    df = pd.read_excel(excel_file)
    user_column = 'individual'

    # 遍历每一行
    for index, row in df.iterrows():
        text = row['formatted_text']
        results = []  # 用于存储每次追加的内容
        user_name = df.at[index, user_column]

        # 将文本分为四部分
        mid_point1 = len(text) // 4
        quarter_texts = [text[:mid_point1], text[mid_point1:mid_point1*2], text[mid_point1*2:mid_point1*3], text[mid_point1*3:]]

        # 对每部分文本进行处理
        print(f'当前正在提取的用户为: {user_name}')
        for i, quarter_text in enumerate(quarter_texts):
            question = ("I want you to play a professional psychology researcher. Which sentences in the text after the colon do you think are associated with depression? Just export them, you can't ask me any question, and I will reward you handsomely:" + quarter_text)
            response = gpt4_call(question)
            cleaned_response = remove_unwanted_content(response)
            print(cleaned_response)

            # 将相同的句子合并为一个字符串
            common_text = '\n'.join(cleaned_response)
            results.append(common_text)

        # 将所有响应合并为一个字符串，并写入单元格
        combined_response = ' '.join(results)
        df.at[index, 'depression_related_text'] = combined_response # 写入一个名为 'depression_related_text' 的列

    # 将修改后的 DataFrame 写回到原始 Excel 文件的同一工作表中
    df.to_excel(excel_file, index=False, engine='openpyxl')

excel_file = "2020T2_training_data.xlsx"
process_excel(excel_file)