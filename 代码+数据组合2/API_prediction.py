from openai import OpenAI
from dotenv import load_dotenv
import pandas as pd
import os
import re
import math
from CLEF_eRisk import *


# 加载.env文件中的环境变量
load_dotenv('API_KEY.env')

# 创建一个OpenAI客户端实例
client = OpenAI(
    api_key=os.getenv("OPENAI_API_KEY"),
    base_url=os.getenv("OPENAI_BASE_URL")
)

def gpt4_call(prompt):
    try:
        completion = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}]
        )
        return completion.choices[0].message.content
    except Exception as e:
        return str(e)

def interactive_dialogue(prompt):
    response = gpt4_call(prompt)
    return response

def process_excel(excel_file):
    df = pd.read_excel(excel_file)

    # 指定所需列
    start_column = 'Q1'
    end_column = 'Q21'
    user_column = 'individual'

    # 确定要遍历的列的索引范围
    start_idx = df.columns.get_loc(start_column)
    end_idx = df.columns.get_loc(end_column)
    j = 1

    # 外层行，内层列
    for index, row in df.iterrows():
        text = row['depression_related_text']
        # text = row['formatted_text']
        user_name = df.at[index, user_column]
        print(f'User {j} : {user_name}\n====================================================================================================')
        results = []
        for i in range(20):
            question = '''Let's think step by step. 
                        1.Mission background: As a professional psychology researcher, you have been assigned to assist me in doing the high-stakes task, and together
                        we will solve the psychological problems of many users and judge the psychological state of users.
                        2.Questionnaire description: You will first receive a question, the phrase after the sequence number represents a certain state, and
                        the options from 0 to 3 represent the degree of state shown gradually. Sadness represents how sad the user is. Options ranging from 0
                        to 3 indicate that the user is not sad to very sad. I will send you a description of the options for the question so that you can make a judgment.
                        3.Specific task: You will then receive a text written by a user, and based on the content, you will need to determine how closely the
                        user corresponds to the number of states the question examines. You should focus on the emotion-related content in the user's text, which
                        is an important basis for judging your options. If you can't judge a particular question accurately, you can use similar questions
                        Judge. For example, users may choose similar feelings for Sadness and Guilty feelings.
                        4.Mission objective: First describe the basis for your judgment, why the user's text makes you think the user will choose the option
                        you predict, then state that the text reflects his emotional state, and finally give a number indicating which option you think he will choose.
                        5.JUST GENERATE THE NUMBER! YOU DON'T NEED ANY MORE SPECIFIC INFORMATION!''' + str(eRisk[i]) + '''user's text: ''' + str(text)

            response = interactive_dialogue(question)
            print("GPT's response: \n", response)
            number = re.findall(r'\d+', response)
            print(f"\nFinal prediction: {number[-1]}\n----------------------------------------------------------------------------------------------------")
            results.append(number[-1])

        # 填充结果到 DataFrame 中
        print(results)
        for col_idx, col_name in enumerate(df.columns[start_idx:end_idx + 1], start=start_idx):
            # df.at[index, col_name] = math.ceil(results[col_idx - start_idx])
            df.at[index, col_name] = results[col_idx - start_idx]
        j += 1

    # 将修改后的 DataFrame 写回到原始 Excel 文件的同一工作表中
    df.to_excel(excel_file, index=False, engine='openpyxl')


excel_file = "2020T2_training_data.xlsx"
process_excel(excel_file)
